/**
 * Simple test script to verify the endpoint is working
 * Run this in the browser console or as a Node.js script
 */

// Test function for browser console
async function testEndpoint() {
  try {
    console.log('🧪 Testing endpoint: /api/member-verification/total-verified-members');
    
    const response = await fetch('/api/member-verification/total-verified-members', {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });
    
    console.log('📡 Response status:', response.status);
    console.log('📡 Response headers:', Object.fromEntries(response.headers.entries()));
    console.log('📡 Response URL:', response.url);
    
    const responseText = await response.text();
    console.log('📡 Raw response:', responseText);
    
    if (response.ok) {
      try {
        const data = JSON.parse(responseText);
        console.log('✅ Success! Parsed data:', data);
        return data;
      } catch (parseError) {
        console.log('⚠️ Response is not JSON:', responseText);
        return responseText;
      }
    } else {
      console.error('❌ HTTP Error:', response.status, response.statusText);
      console.error('❌ Error response:', responseText);
      throw new Error(`HTTP ${response.status}: ${responseText}`);
    }
  } catch (error) {
    console.error('❌ Network or other error:', error);
    throw error;
  }
}

// Test function for checking if the route exists
async function checkRouteExists() {
  try {
    console.log('🔍 Checking if route exists...');
    
    const response = await fetch('/api/member-verification/total-verified-members', {
      method: 'OPTIONS', // CORS preflight
    });
    
    console.log('📡 OPTIONS response status:', response.status);
    console.log('📡 OPTIONS response headers:', Object.fromEntries(response.headers.entries()));
    
    if (response.status === 200) {
      console.log('✅ Route exists and responds to OPTIONS');
      return true;
    } else {
      console.log('❌ Route may not exist or has issues');
      return false;
    }
  } catch (error) {
    console.error('❌ Error checking route:', error);
    return false;
  }
}

// Test authentication status
async function checkAuthStatus() {
  try {
    console.log('🔐 Checking authentication status...');
    
    // Check for Cognito tokens in cookies
    const cookies = document.cookie.split(';').reduce((acc, cookie) => {
      const [key, value] = cookie.trim().split('=');
      acc[key] = value;
      return acc;
    }, {});
    
    const hasAccessToken = !!cookies.cognito_access_token;
    const hasIdToken = !!cookies.cognito_id_token;
    const hasLegacyToken = !!cookies.token;
    
    console.log('🔐 Authentication tokens found:');
    console.log('  - Cognito Access Token:', hasAccessToken ? '✅' : '❌');
    console.log('  - Cognito ID Token:', hasIdToken ? '✅' : '❌');
    console.log('  - Legacy Token:', hasLegacyToken ? '✅' : '❌');
    
    if (hasAccessToken && hasIdToken) {
      console.log('✅ Admin authentication detected (Cognito)');
      return 'admin';
    } else if (hasLegacyToken) {
      console.log('✅ Legacy authentication detected');
      return 'legacy';
    } else {
      console.log('❌ No authentication tokens found');
      return null;
    }
  } catch (error) {
    console.error('❌ Error checking auth status:', error);
    return null;
  }
}

// Comprehensive test function
async function runComprehensiveTest() {
  console.log('🚀 Starting comprehensive endpoint test...');
  console.log('='.repeat(50));
  
  try {
    // Step 1: Check authentication
    const authStatus = await checkAuthStatus();
    console.log('\n1️⃣ Authentication Status:', authStatus || 'Not authenticated');
    
    // Step 2: Check if route exists
    const routeExists = await checkRouteExists();
    console.log('\n2️⃣ Route Exists:', routeExists ? '✅' : '❌');
    
    // Step 3: Test the actual endpoint
    console.log('\n3️⃣ Testing endpoint...');
    const result = await testEndpoint();
    
    console.log('\n🎉 All tests completed successfully!');
    console.log('📊 Final result:', result);
    
    return {
      authStatus,
      routeExists,
      result,
      success: true
    };
    
  } catch (error) {
    console.error('\n❌ Test failed:', error.message);
    
    // Provide helpful debugging information
    console.log('\n🔧 Debugging information:');
    console.log('- Make sure you are logged in (admin or member)');
    console.log('- Check that the Next.js server is running');
    console.log('- Verify the backend API is accessible from the Next.js server');
    console.log('- Check browser console for additional error details');
    
    return {
      authStatus: await checkAuthStatus(),
      routeExists: await checkRouteExists(),
      error: error.message,
      success: false
    };
  }
}

// Make functions available globally for browser console testing
if (typeof window !== 'undefined') {
  window.testEndpoint = testEndpoint;
  window.checkRouteExists = checkRouteExists;
  window.checkAuthStatus = checkAuthStatus;
  window.runComprehensiveTest = runComprehensiveTest;
  
  console.log('🧪 Test functions available in browser console:');
  console.log('- testEndpoint()');
  console.log('- checkRouteExists()');
  console.log('- checkAuthStatus()');
  console.log('- runComprehensiveTest()');
}

// Export for Node.js if needed
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    testEndpoint,
    checkRouteExists,
    checkAuthStatus,
    runComprehensiveTest
  };
}
