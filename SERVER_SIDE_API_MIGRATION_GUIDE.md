# Server-Side API Migration Guide

## Overview

This guide explains how to migrate from client-side direct backend API calls to server-side proxy calls in your Next.js application. This approach eliminates the need for VPN access for end users while maintaining secure server-to-server communication.

## Architecture Changes

### Before (Client-Side Direct Calls)
```
<PERSON><PERSON><PERSON> → <PERSON><PERSON> → Python Backend
```
- Users need VPN access
- Authentication tokens exposed to client
- CORS configuration required
- Direct network dependency

### After (Server-Side Proxy)
```
<PERSON>rowser → Next.js Server → Python Backend
```
- No VPN required for users
- Authentication handled server-side
- Secure server-to-server communication
- Better error handling and security

## Implementation Components

### 1. Server-Side API Client (`src/lib/server-api-client.ts`)
- Handles authentication token extraction from cookies
- Makes authenticated requests to Python backend
- Provides error handling and logging

### 2. Authentication Utilities (`src/lib/server-auth.ts`)
- Validates both Cognito (admin) and Auth0 (member) authentication
- Extracts user context from tokens
- Handles token expiration and refresh logic

### 3. API Proxy Routes
- **Generic Proxy**: `/api/proxy/[...path]` - Forwards any request to backend
- **Specific Routes**: `/api/admin/users`, `/api/admin/users/[id]` - Type-safe endpoints

### 4. Client-Side API Client (`src/lib/client-api.ts`)
- Replaces direct backend calls with proxy calls
- Handles authentication errors and redirects
- Provides migration utilities

### 5. Security Layer (`src/lib/api-security.ts`)
- Rate limiting
- Request validation
- Security headers
- Error sanitization

## Migration Steps

### Step 1: Update Environment Variables

Add server-side backend URL (without NEXT_PUBLIC prefix):
```env
# Server-side backend URL (not exposed to client)
BACKEND_API_URL=http://your-python-backend:8000

# Keep existing client-side URL for gradual migration
NEXT_PUBLIC_BACKEND_API_URL=http://your-python-backend:8000
```

### Step 2: Replace API Calls

#### Before (Direct Backend Call)
```typescript
import { apiClient } from '@/services/apiClient';

// Direct call to backend
const response = await apiClient.get('/api/admin/users');
```

#### After (Server-Side Proxy)
```typescript
import { api } from '@/lib/client-api';

// Call through server-side proxy
const response = await api.get('/api/admin/users');
```

### Step 3: Update Service Files

#### Before (`src/services/adminAPI.ts`)
```typescript
export async function getAdminUsers() {
  const token = getAccessToken(); // Client-side token
  const response = await apiClient.get('/api/admin/users', {
    headers: { Authorization: `Bearer ${token}` }
  });
  return response.data;
}
```

#### After (`src/services/adminAPI-serverside.ts`)
```typescript
import { adminApi } from '@/lib/client-api';

export async function getAdminUsers() {
  // No token handling needed - done server-side
  const response = await adminApi.getUsers();
  return response.data;
}
```

### Step 4: Update React Components

#### Before
```typescript
import { useEffect, useState } from 'react';
import { getAdminUsers } from '@/services/adminAPI';

function AdminUsersPage() {
  const [users, setUsers] = useState([]);
  
  useEffect(() => {
    getAdminUsers().then(setUsers);
  }, []);
  
  return <div>{/* render users */}</div>;
}
```

#### After
```typescript
import { useEffect, useState } from 'react';
import { adminAPIService } from '@/services/adminAPI-serverside';

function AdminUsersPage() {
  const [users, setUsers] = useState([]);
  
  useEffect(() => {
    adminAPIService.getUsers().then(setUsers);
  }, []);
  
  return <div>{/* render users */}</div>;
}
```

## API Endpoint Mapping

| Backend Endpoint | Proxy Endpoint | Specific Route |
|------------------|----------------|----------------|
| `/api/admin/users` | `/api/proxy/api/admin/users` | `/api/admin/users` |
| `/api/admin/users/{id}` | `/api/proxy/api/admin/users/{id}` | `/api/admin/users/{id}` |
| `/api/members` | `/api/proxy/api/members` | - |
| `/api/organizations` | `/api/proxy/api/organizations` | - |

## Authentication Handling

### Admin Users (Cognito)
- Tokens stored in cookies: `cognito_access_token`, `cognito_id_token`, `cognito_refresh_token`
- Server-side extraction and validation
- Automatic token refresh handling

### Member Users (Auth0)
- Session-based authentication
- Server-side session validation
- Integrated with existing Auth0 setup

## Error Handling

### Client-Side Error Handling
```typescript
try {
  const response = await api.get('/api/admin/users');
  return response.data;
} catch (error) {
  if (error.message.includes('Authentication failed')) {
    // Redirect to login
    window.location.href = '/login';
  }
  throw error;
}
```

### Server-Side Error Responses
```json
{
  "error": "Authentication failed",
  "code": "AUTHENTICATION_FAILED",
  "details": "Token expired"
}
```

## Security Features

### Rate Limiting
- 100 requests per 15-minute window per client
- Configurable limits
- In-memory storage (upgrade to Redis for production)

### Request Validation
- Origin validation
- Request size limits
- Header validation
- Path traversal protection

### Security Headers
- Content Security Policy
- XSS Protection
- Frame Options
- HSTS

## Testing

### Test Server-Side Proxy
```bash
# Test authentication
curl -X GET http://localhost:3000/api/admin/users \
  -H "Cookie: cognito_access_token=your-token"

# Test rate limiting
for i in {1..105}; do
  curl -X GET http://localhost:3000/api/proxy/api/admin/users
done
```

### Test Client-Side Integration
```typescript
// Test in browser console
import { api } from '@/lib/client-api';
api.get('/api/admin/users').then(console.log);
```

## Deployment Considerations

### Environment Variables
```env
# Production
BACKEND_API_URL=http://internal-backend:8000
NODE_ENV=production

# Development
BACKEND_API_URL=http://localhost:8000
NODE_ENV=development
```

### Network Configuration
- Ensure Next.js server can reach Python backend
- Configure internal DNS/service discovery
- Set up proper firewall rules for server-to-server communication

### Monitoring
- Add logging for proxy requests
- Monitor rate limiting metrics
- Track authentication failures
- Set up alerts for backend connectivity issues

## Gradual Migration Strategy

1. **Phase 1**: Deploy proxy infrastructure alongside existing direct calls
2. **Phase 2**: Migrate admin user management endpoints
3. **Phase 3**: Migrate member-related endpoints
4. **Phase 4**: Migrate remaining endpoints
5. **Phase 5**: Remove direct backend access and VPN requirement

## Troubleshooting

### Common Issues

1. **Authentication Failures**
   - Check cookie names and values
   - Verify token expiration
   - Ensure middleware is working

2. **CORS Errors**
   - Verify security headers configuration
   - Check allowed origins

3. **Rate Limiting**
   - Adjust rate limit configuration
   - Implement user-specific limits

4. **Backend Connectivity**
   - Verify BACKEND_API_URL
   - Check network connectivity
   - Review firewall rules

### Debug Mode
Set `NODE_ENV=development` for detailed error messages and logging.

## Benefits Achieved

✅ **No VPN Required**: End users can access the application without VPN  
✅ **Enhanced Security**: Authentication handled server-side  
✅ **Better Performance**: Reduced client-side complexity  
✅ **Improved Monitoring**: Centralized API logging and metrics  
✅ **Rate Limiting**: Built-in protection against abuse  
✅ **Error Handling**: Consistent error responses  
✅ **Type Safety**: TypeScript support for API calls  

## Next Steps

1. Deploy the proxy infrastructure
2. Test with a small subset of endpoints
3. Gradually migrate existing API calls
4. Monitor performance and security metrics
5. Remove VPN requirement once migration is complete
