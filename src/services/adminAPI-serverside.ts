/**
 * Updated Admin API service using server-side proxy endpoints
 * This replaces direct backend calls with calls to Next.js API routes
 */

import { api, adminApi } from '@/lib/client-api';

// Types (keep existing types)
export interface AdminUser {
  uuid: string;
  username: string;
  email: string;
  roles: string[];
  firstName?: string;
  lastName?: string;
  phone?: string;
  countrycode?: string;
  isActive: boolean;
  last_login?: string;
  created_at: string;
  updated_at: string;
}

export interface CreateAdminUserRequest {
  email: string;
  username: string;
  password: string;
  roles?: string[];
  firstName?: string;
  lastName?: string;
  phone?: string;
  countrycode?: string;
}

export interface UpdateAdminUserRequest {
  email?: string;
  username?: string;
  roles?: string[];
  firstName?: string;
  lastName?: string;
  phone?: string;
  countrycode?: string;
  isActive?: boolean;
}

export interface AdminUserFilters {
  page?: number;
  limit?: number;
  search?: string;
  role?: string;
  isActive?: boolean;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export interface AdminUserListResponse {
  users: AdminUser[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

export interface ApiResponse<T> {
  success: boolean;
  data: T;
  message?: string;
  status_code: number;
}

/**
 * Admin API service using server-side proxy
 */
export const adminAPIService = {
  /**
   * Get list of admin users with filtering and pagination
   */
  async getUsers(filters: AdminUserFilters = {}): Promise<AdminUserListResponse> {
    try {
      console.log('🔍 Fetching admin users via server-side proxy:', filters);
      
      const response = await adminApi.getUsers(filters);
      return response.data;
    } catch (error: any) {
      console.error('❌ Error fetching admin users:', error);
      throw new Error(error.message || 'Failed to fetch admin users');
    }
  },

  /**
   * Get a specific admin user by ID
   */
  async getUser(userId: string): Promise<AdminUser> {
    try {
      console.log(`🔍 Fetching admin user ${userId} via server-side proxy`);
      
      const response = await adminApi.getUser(userId);
      return response.data;
    } catch (error: any) {
      console.error(`❌ Error fetching admin user ${userId}:`, error);
      throw new Error(error.message || 'Failed to fetch admin user');
    }
  },

  /**
   * Create a new admin user
   */
  async createUser(userData: CreateAdminUserRequest): Promise<AdminUser> {
    try {
      console.log('👤 Creating admin user via server-side proxy:', userData.email);
      
      const response = await adminApi.createUser(userData);
      return response.data;
    } catch (error: any) {
      console.error('❌ Error creating admin user:', error);
      throw new Error(error.message || 'Failed to create admin user');
    }
  },

  /**
   * Update an existing admin user
   */
  async updateUser(userId: string, userData: UpdateAdminUserRequest): Promise<AdminUser> {
    try {
      console.log(`✏️ Updating admin user ${userId} via server-side proxy`);
      
      const response = await adminApi.updateUser(userId, userData);
      return response.data;
    } catch (error: any) {
      console.error(`❌ Error updating admin user ${userId}:`, error);
      throw new Error(error.message || 'Failed to update admin user');
    }
  },

  /**
   * Delete an admin user
   */
  async deleteUser(userId: string): Promise<{ success: boolean; message: string }> {
    try {
      console.log(`🗑️ Deleting admin user ${userId} via server-side proxy`);
      
      const response = await adminApi.deleteUser(userId);
      return response.data;
    } catch (error: any) {
      console.error(`❌ Error deleting admin user ${userId}:`, error);
      throw new Error(error.message || 'Failed to delete admin user');
    }
  },

  /**
   * Register a new admin user (alternative endpoint)
   */
  async registerAdmin(userData: CreateAdminUserRequest): Promise<AdminUser> {
    try {
      console.log('👤 Registering admin user via server-side proxy:', userData.email);
      
      // Use the proxy endpoint for admin registration
      const response = await api.post('/api/admin/register', userData);
      return response.data;
    } catch (error: any) {
      console.error('❌ Error registering admin user:', error);
      throw new Error(error.message || 'Failed to register admin user');
    }
  },

  /**
   * Get admin user roles
   */
  async getUserRoles(): Promise<string[]> {
    try {
      console.log('🔍 Fetching admin user roles via server-side proxy');
      
      const response = await api.get('/api/admin/roles');
      return response.data;
    } catch (error: any) {
      console.error('❌ Error fetching admin user roles:', error);
      throw new Error(error.message || 'Failed to fetch admin user roles');
    }
  },

  /**
   * Update user roles
   */
  async updateUserRoles(userId: string, roles: string[]): Promise<AdminUser> {
    try {
      console.log(`🔧 Updating roles for admin user ${userId} via server-side proxy`);
      
      const response = await api.put(`/api/admin/users/${userId}/roles`, { roles });
      return response.data;
    } catch (error: any) {
      console.error(`❌ Error updating roles for admin user ${userId}:`, error);
      throw new Error(error.message || 'Failed to update user roles');
    }
  },

  /**
   * Get admin user statistics
   */
  async getUserStats(): Promise<any> {
    try {
      console.log('📊 Fetching admin user statistics via server-side proxy');
      
      const response = await api.get('/api/admin/users/stats');
      return response.data;
    } catch (error: any) {
      console.error('❌ Error fetching admin user statistics:', error);
      throw new Error(error.message || 'Failed to fetch admin user statistics');
    }
  },

  /**
   * Export admin users
   */
  async exportUsers(filters: AdminUserFilters = {}): Promise<Blob> {
    try {
      console.log('📤 Exporting admin users via server-side proxy');
      
      // For file downloads, we might need to handle this differently
      const response = await api.proxy('/api/admin/users/export', {
        method: 'POST',
        data: filters,
        responseType: 'blob'
      });
      
      return response.data;
    } catch (error: any) {
      console.error('❌ Error exporting admin users:', error);
      throw new Error(error.message || 'Failed to export admin users');
    }
  }
};

/**
 * Migration helper functions
 */
export const migrationHelpers = {
  /**
   * Convert old API calls to new server-side proxy calls
   * This can be used to gradually migrate existing code
   */
  convertApiCall: (oldEndpoint: string, method: string = 'GET', data?: any) => {
    console.log(`🔄 Converting API call: ${method} ${oldEndpoint}`);
    
    switch (method.toUpperCase()) {
      case 'GET':
        return api.get(oldEndpoint);
      case 'POST':
        return api.post(oldEndpoint, data);
      case 'PUT':
        return api.put(oldEndpoint, data);
      case 'PATCH':
        return api.patch(oldEndpoint, data);
      case 'DELETE':
        return api.delete(oldEndpoint);
      default:
        throw new Error(`Unsupported HTTP method: ${method}`);
    }
  },

  /**
   * Batch convert multiple API calls
   */
  batchConvert: async (calls: Array<{ endpoint: string; method: string; data?: any }>) => {
    console.log(`🔄 Converting ${calls.length} API calls to server-side proxy`);
    
    const promises = calls.map(call => 
      migrationHelpers.convertApiCall(call.endpoint, call.method, call.data)
    );
    
    return Promise.all(promises);
  }
};

// Export the service and types
export default adminAPIService;
export type {
  AdminUser,
  CreateAdminUserRequest,
  UpdateAdminUserRequest,
  AdminUserFilters,
  AdminUserListResponse,
  ApiResponse
};
