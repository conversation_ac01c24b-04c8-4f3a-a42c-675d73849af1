// MIGRATION NOTE: This file shows the OLD way of making direct backend API calls
// For the NEW server-side proxy approach, see adminAPI-serverside.ts
//
// This file is kept for reference and gradual migration
// TODO: Replace all usages with adminAPI-serverside.ts

import { getAuthToken, getCognitoTokensFromCookies } from "@/utils/auth";
import { apiClient } from "./apiClient";

export interface CreateAdminUserRequest {
  email: string;
  username: string;
  password: string;
  roles?: ("super_admin" | "admin" | "moderator")[];
}

export interface CreateAdminUserResponse {
  success: boolean;
  message?: string;
  error?: string;
  details?: string;
  data?: {
    userId: string;
    username: string;
    email: string;
    role: string;
    status: string;
    createdBy: string;
    createdAt: string;
  };
}

/**
 * Get the current user's access token for API calls
 */
function getAccessToken(): string | null {
  // Try to get token from cookies first (synced from Cognito)
  const cognitoTokens = getCognitoTokensFromCookies();
  if (cognitoTokens.accessToken) {
    return cognitoTokens.accessToken;
  }

  // Fallback to legacy token
  return getAuthToken();
}

/**
 * Create a new admin user
 */
export async function createAdminUser(
  userData: CreateAdminUserRequest
): Promise<CreateAdminUserResponse> {
  try {
    const accessToken = getAccessToken();

    if (!accessToken) {
      throw new Error("No access token found. Please log in again.");
    }

    const response = await apiClient.post(`/api/admin/register`, userData);

    return response.data;
  } catch (error: any) {
    console.error("Create admin user error:", error);

    // Handle axios errors
    const errorMessage =
      error.response?.data?.error ||
      error.response?.data?.message ||
      error.message ||
      "Failed to create admin user";

    return {
      success: false,
      error: errorMessage,
      details: error.response?.data?.details || "An unexpected error occurred",
    };
  }
}

/**
 * Get list of admin users (placeholder for future implementation)
 */
export async function getAdminUsers(): Promise<any> {
  try {
    const response = await apiClient.get("/api/admin/users");
    return response.data;
  } catch (error: any) {
    console.error("Get admin users error:", error);

    // Handle axios errors
    if (error.response) {
      throw new Error(`HTTP error! status: ${error.response.status}`);
    }

    // Handle network errors
    if (error.request) {
      throw new Error("Network error. Please check your connection.");
    }

    // Handle other errors
    throw new Error(error.message || "Failed to get admin users");
  }
}

/**
 * Fetch a single admin user by UUID
 * @param uuid The UUID of the admin user to fetch
 * @returns AdminUser object
 */
export async function fetchAdminUserByUuid(uuid: string): Promise<any> {
  try {
    const response = await apiClient.get(`/api/admin/${uuid}`);
    const responseData = response.data;

    // Check if the response has the expected structure
    if (!responseData.success || !responseData.admin) {
      throw new Error(responseData.message || "Invalid response format");
    }

    const adminData = responseData.admin;
    console.log("Raw API response:", responseData);
    console.log("Admin data:", adminData);

    // Transform the response to match AdminUser interface
    return {
      uuid: adminData.uuid,
      username: adminData.username || "",
      email: adminData.email || "",
      firstName: adminData.firstName || null,
      lastName: adminData.lastName || null,
      phone: adminData.phone || null,
      countrycode: adminData.countrycode || null,
      isactive: adminData.isactive !== undefined ? adminData.isactive : true,
      istemppassword: adminData.istemppassword || false,
      emailVerified: adminData.emailVerified || false,
      roles: Array.isArray(adminData.roles)
        ? adminData.roles
        : [adminData.roles],
      createdBy: adminData.createdBy || "",
      permissions: adminData.permissions || [],
      // Additional fields from API response
      cognitoid: adminData.cognitoid || null,
      updatedBy: adminData.updatedBy || null,
      lastlogin: adminData.lastlogin || null,
      dateCreated: adminData.dateCreated || null,
      dateUpdated: adminData.dateUpdated || null,
    };
  } catch (error: any) {
    console.error("Fetch admin user by UUID error:", error);

    // Handle specific HTTP status codes
    if (error.response?.status === 404) {
      throw new Error("Admin user not found");
    }

    if (error.response?.status === 403) {
      throw new Error("You don't have permission to access this user");
    }

    // Handle other axios errors
    if (error.response) {
      throw new Error(`HTTP error! status: ${error.response.status}`);
    }

    // Handle network errors
    if (error.request) {
      throw new Error("Network error. Please check your connection.");
    }

    // Handle other errors
    throw new Error(error.message || "Failed to fetch admin user");
  }
}

/**
 * Update an admin user using the /api/admin/{uuid} endpoint
 */
export async function updateAdminUser(
  uuid: string,
  userData: {
    password?: string;
    email?: string;
    firstName?: string;
    lastName?: string;
    phone?: string;
    countrycode?: string;
    isactive?: boolean;
    istemppassword?: boolean;
    roles?: string[];
  }
): Promise<any> {
  try {
    // Only include the allowed fields in the request body
    const requestBody = {
      ...(userData.password && { password: userData.password }),
      ...(userData.email && { email: userData.email }),
      ...(userData.firstName && { firstName: userData.firstName }),
      ...(userData.lastName && { lastName: userData.lastName }),
      ...(userData.phone && { phone: userData.phone }),
      ...(userData.countrycode && { countrycode: userData.countrycode }),
      ...(userData.isactive !== undefined && { isactive: userData.isactive }),
      ...(userData.istemppassword !== undefined && {
        istemppassword: userData.istemppassword,
      }),
      ...(userData.roles && { roles: userData.roles }),
    };

    const response = await apiClient.put(`/api/admin/${uuid}`, requestBody);

    // Return the API response directly as it matches the expected structure
    return response.data;
  } catch (error: any) {
    console.error("Update admin user error:", error);

    // Handle axios errors
    const errorMessage =
      error.response?.data?.message ||
      error.message ||
      "Failed to update admin user";

    return {
      success: false,
      error: errorMessage,
    };
  }
}

/**
 * Delete an admin user using the /api/admin/{uuid} endpoint
 */
export async function deleteAdminUser(uuid: string): Promise<any> {
  try {
    const response = await apiClient.delete(`/api/admin/${uuid}`);

    return {
      success: true,
      data: response.data,
      message: "Admin user deleted successfully",
    };
  } catch (error: any) {
    console.error("Delete admin user error:", error);

    // Handle axios errors
    const errorMessage =
      error.response?.data?.message ||
      error.message ||
      "Failed to delete admin user";

    return {
      success: false,
      error: errorMessage,
    };
  }
}

/**
 * Fetch the current admin user
 */
export async function fetchCurrentAdminUser(): Promise<any> {
  try {
    const response = await apiClient.get(`/api/admin/get-admin-user`);
    return response.data;
  } catch (error: any) {
    console.error("Fetch current admin user error:", error);

    // Handle axios errors
    if (error.response) {
      throw new Error(`HTTP error! status: ${error.response.status}`);
    }

    // Handle network errors
    if (error.request) {
      throw new Error("Network error. Please check your connection.");
    }

    // Handle other errors
    throw new Error(error.message || "Failed to fetch current admin user");
  }
}
