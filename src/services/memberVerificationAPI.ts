/**
 * Member Verification API service using server-side proxy
 * Test implementation for /api/member-verification/total-verified-members
 */

import React from 'react';
import { api } from '@/lib/client-api';

// Types
export interface TotalVerifiedMembersResponse {
  total_verified_members: number;
  last_updated?: string;
  success: boolean;
  message?: string;
}

export interface ApiResponse<T> {
  data: T;
  status: number;
  statusText: string;
}

/**
 * Member Verification API service
 */
export const memberVerificationAPI = {
  /**
   * Get total count of verified members
   * Tests the server-side proxy with the specified endpoint
   */
  async getTotalVerifiedMembers(): Promise<TotalVerifiedMembersResponse> {
    try {
      console.log('🔍 Fetching total verified members via server-side proxy');
      
      const response = await api.get('/api/member-verification/total-verified-members');
      
      console.log('✅ Successfully fetched total verified members:', response.data);
      return response.data;
    } catch (error: any) {
      console.error('❌ Error fetching total verified members:', error);
      
      // Handle specific error cases
      if (error.message?.includes('Authentication failed')) {
        throw new Error('Authentication required. Please log in again.');
      }
      
      if (error.message?.includes('Access forbidden')) {
        throw new Error('You do not have permission to access this data.');
      }
      
      if (error.message?.includes('Backend service unavailable')) {
        throw new Error('Service temporarily unavailable. Please try again later.');
      }
      
      throw new Error(error.message || 'Failed to fetch total verified members');
    }
  },

  /**
   * Test function to demonstrate the proxy working
   * This can be called from browser console or React components
   */
  async testProxyEndpoint(): Promise<void> {
    try {
      console.log('🧪 Testing server-side proxy endpoint...');
      
      const startTime = performance.now();
      const result = await this.getTotalVerifiedMembers();
      const endTime = performance.now();
      
      console.log('🎉 Proxy test successful!');
      console.log('📊 Result:', result);
      console.log(`⏱️ Response time: ${(endTime - startTime).toFixed(2)}ms`);
      console.log('✅ Server-side proxy is working correctly');
      
      return result;
    } catch (error) {
      console.error('❌ Proxy test failed:', error);
      throw error;
    }
  }
};

/**
 * React hook for using total verified members data
 */
export function useTotalVerifiedMembers() {
  const [data, setData] = React.useState<TotalVerifiedMembersResponse | null>(null);
  const [loading, setLoading] = React.useState(false);
  const [error, setError] = React.useState<string | null>(null);

  const fetchData = React.useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      
      const result = await memberVerificationAPI.getTotalVerifiedMembers();
      setData(result);
    } catch (err: any) {
      setError(err.message);
      console.error('Error in useTotalVerifiedMembers:', err);
    } finally {
      setLoading(false);
    }
  }, []);

  React.useEffect(() => {
    fetchData();
  }, [fetchData]);

  return {
    data,
    loading,
    error,
    refetch: fetchData
  };
}

// Export for easy testing
export default memberVerificationAPI;
