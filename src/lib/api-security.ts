/**
 * Security utilities for server-side API proxy
 * Handles request validation, rate limiting, and security headers
 */

import { NextRequest } from 'next/server';
import { headers } from 'next/headers';

// Types
export interface SecurityConfig {
  enableRateLimit?: boolean;
  rateLimitRequests?: number;
  rateLimitWindow?: number; // in milliseconds
  enableRequestValidation?: boolean;
  allowedOrigins?: string[];
  maxRequestSize?: number; // in bytes
}

export interface RateLimitInfo {
  isAllowed: boolean;
  remaining: number;
  resetTime: number;
  error?: string;
}

export interface ValidationResult {
  isValid: boolean;
  errors: string[];
}

// In-memory rate limiting store (in production, use Redis or similar)
const rateLimitStore = new Map<string, { count: number; resetTime: number }>();

/**
 * Default security configuration
 */
const DEFAULT_SECURITY_CONFIG: SecurityConfig = {
  enableRateLimit: true,
  rateLimitRequests: 100, // 100 requests per window
  rateLimitWindow: 15 * 60 * 1000, // 15 minutes
  enableRequestValidation: true,
  allowedOrigins: ['http://localhost:3000', 'https://yourdomain.com'],
  maxRequestSize: 10 * 1024 * 1024 // 10MB
};

/**
 * Get client identifier for rate limiting
 */
function getClientIdentifier(request: NextRequest): string {
  // Try to get user ID from auth context first
  const userAgent = request.headers.get('user-agent') || '';
  const forwarded = request.headers.get('x-forwarded-for');
  const realIp = request.headers.get('x-real-ip');
  const ip = forwarded?.split(',')[0] || realIp || request.ip || 'unknown';
  
  // Create a composite identifier
  return `${ip}:${userAgent.slice(0, 50)}`;
}

/**
 * Rate limiting implementation
 */
export function checkRateLimit(
  request: NextRequest,
  config: SecurityConfig = DEFAULT_SECURITY_CONFIG
): RateLimitInfo {
  if (!config.enableRateLimit) {
    return { isAllowed: true, remaining: Infinity, resetTime: 0 };
  }

  const clientId = getClientIdentifier(request);
  const now = Date.now();
  const windowMs = config.rateLimitWindow || DEFAULT_SECURITY_CONFIG.rateLimitWindow!;
  const maxRequests = config.rateLimitRequests || DEFAULT_SECURITY_CONFIG.rateLimitRequests!;

  // Clean up expired entries
  for (const [key, value] of rateLimitStore.entries()) {
    if (value.resetTime <= now) {
      rateLimitStore.delete(key);
    }
  }

  // Get or create rate limit entry
  let rateLimitEntry = rateLimitStore.get(clientId);
  
  if (!rateLimitEntry || rateLimitEntry.resetTime <= now) {
    // Create new window
    rateLimitEntry = {
      count: 0,
      resetTime: now + windowMs
    };
  }

  // Check if limit exceeded
  if (rateLimitEntry.count >= maxRequests) {
    return {
      isAllowed: false,
      remaining: 0,
      resetTime: rateLimitEntry.resetTime,
      error: 'Rate limit exceeded'
    };
  }

  // Increment counter
  rateLimitEntry.count++;
  rateLimitStore.set(clientId, rateLimitEntry);

  return {
    isAllowed: true,
    remaining: maxRequests - rateLimitEntry.count,
    resetTime: rateLimitEntry.resetTime
  };
}

/**
 * Validate request origin
 */
export function validateOrigin(
  request: NextRequest,
  config: SecurityConfig = DEFAULT_SECURITY_CONFIG
): boolean {
  if (!config.allowedOrigins || config.allowedOrigins.length === 0) {
    return true; // No origin restriction
  }

  const origin = request.headers.get('origin');
  const referer = request.headers.get('referer');

  // Allow same-origin requests
  if (!origin && !referer) {
    return true;
  }

  // Check origin
  if (origin && config.allowedOrigins.includes(origin)) {
    return true;
  }

  // Check referer as fallback
  if (referer) {
    const refererOrigin = new URL(referer).origin;
    if (config.allowedOrigins.includes(refererOrigin)) {
      return true;
    }
  }

  return false;
}

/**
 * Validate request size
 */
export function validateRequestSize(
  request: NextRequest,
  config: SecurityConfig = DEFAULT_SECURITY_CONFIG
): boolean {
  const contentLength = request.headers.get('content-length');
  
  if (!contentLength) {
    return true; // No content length header
  }

  const size = parseInt(contentLength, 10);
  const maxSize = config.maxRequestSize || DEFAULT_SECURITY_CONFIG.maxRequestSize!;

  return size <= maxSize;
}

/**
 * Validate request headers
 */
export function validateHeaders(request: NextRequest): ValidationResult {
  const errors: string[] = [];

  // Check for required headers
  const contentType = request.headers.get('content-type');
  if (['POST', 'PUT', 'PATCH'].includes(request.method) && !contentType) {
    errors.push('Content-Type header is required for this method');
  }

  // Validate content type for JSON requests
  if (contentType && contentType.includes('application/json')) {
    // Additional JSON validation can be added here
  }

  // Check for suspicious headers
  const suspiciousHeaders = ['x-forwarded-host', 'x-original-url'];
  for (const header of suspiciousHeaders) {
    if (request.headers.get(header)) {
      console.warn(`⚠️ Suspicious header detected: ${header}`);
    }
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}

/**
 * Validate request path
 */
export function validatePath(path: string): ValidationResult {
  const errors: string[] = [];

  // Check for path traversal attempts
  if (path.includes('..') || path.includes('//')) {
    errors.push('Invalid path: path traversal detected');
  }

  // Check for suspicious patterns
  const suspiciousPatterns = [
    /\.(php|asp|jsp|cgi)$/i,
    /\.(exe|bat|cmd|sh)$/i,
    /(union|select|insert|delete|drop|create|alter)\s/i
  ];

  for (const pattern of suspiciousPatterns) {
    if (pattern.test(path)) {
      errors.push('Invalid path: suspicious pattern detected');
    }
  }

  // Validate path length
  if (path.length > 2048) {
    errors.push('Invalid path: path too long');
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}

/**
 * Comprehensive request validation
 */
export function validateRequest(
  request: NextRequest,
  config: SecurityConfig = DEFAULT_SECURITY_CONFIG
): ValidationResult {
  const errors: string[] = [];

  // Rate limiting
  const rateLimitResult = checkRateLimit(request, config);
  if (!rateLimitResult.isAllowed) {
    errors.push(rateLimitResult.error || 'Rate limit exceeded');
  }

  // Origin validation
  if (!validateOrigin(request, config)) {
    errors.push('Invalid origin');
  }

  // Request size validation
  if (!validateRequestSize(request, config)) {
    errors.push('Request size too large');
  }

  // Header validation
  if (config.enableRequestValidation) {
    const headerValidation = validateHeaders(request);
    errors.push(...headerValidation.errors);
  }

  // Path validation
  const url = new URL(request.url);
  const pathValidation = validatePath(url.pathname);
  errors.push(...pathValidation.errors);

  return {
    isValid: errors.length === 0,
    errors
  };
}

/**
 * Security headers for responses
 */
export function getSecurityHeaders(): Record<string, string> {
  return {
    'X-Content-Type-Options': 'nosniff',
    'X-Frame-Options': 'DENY',
    'X-XSS-Protection': '1; mode=block',
    'Referrer-Policy': 'strict-origin-when-cross-origin',
    'Content-Security-Policy': "default-src 'self'",
    'Strict-Transport-Security': 'max-age=31536000; includeSubDomains',
    'Cache-Control': 'no-store, no-cache, must-revalidate, proxy-revalidate',
    'Pragma': 'no-cache',
    'Expires': '0'
  };
}

/**
 * Sanitize error messages for production
 */
export function sanitizeError(error: any, isDevelopment: boolean = false): string {
  if (isDevelopment) {
    return error.message || 'Unknown error';
  }

  // In production, return generic error messages
  if (error.response?.status === 401) {
    return 'Authentication required';
  }

  if (error.response?.status === 403) {
    return 'Access forbidden';
  }

  if (error.response?.status === 404) {
    return 'Resource not found';
  }

  if (error.response?.status >= 500) {
    return 'Internal server error';
  }

  return 'An error occurred';
}

// Export types and functions
export type { SecurityConfig, RateLimitInfo, ValidationResult };
export {
  DEFAULT_SECURITY_CONFIG,
  checkRateLimit,
  validateOrigin,
  validateRequestSize,
  validateHeaders,
  validatePath,
  validateRequest,
  getSecurityHeaders,
  sanitizeError
};
