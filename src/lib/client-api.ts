/**
 * Client-side API utilities for making requests to server-side proxy endpoints
 * This replaces direct backend API calls with calls to Next.js API routes
 */

import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';

// Types
export interface ClientApiConfig {
  baseURL?: string;
  timeout?: number;
  headers?: Record<string, string>;
}

export interface ApiError {
  error: string;
  code?: string;
  details?: string;
}

export interface ApiResponse<T = any> {
  data: T;
  status: number;
  statusText: string;
}

/**
 * Client-side API client that uses server-side proxy endpoints
 */
export class ClientApiClient {
  private axiosInstance: AxiosInstance;
  
  constructor(config: ClientApiConfig = {}) {
    this.axiosInstance = axios.create({
      baseURL: config.baseURL || '/api',
      timeout: config.timeout || 30000,
      headers: {
        'Content-Type': 'application/json',
        ...config.headers
      }
    });
    
    // Request interceptor for logging
    this.axiosInstance.interceptors.request.use(
      (config) => {
        console.log(`🚀 Client API Request: ${config.method?.toUpperCase()} ${config.url}`);
        return config;
      },
      (error) => {
        console.error('❌ Client API Request Error:', error);
        return Promise.reject(error);
      }
    );
    
    // Response interceptor for error handling
    this.axiosInstance.interceptors.response.use(
      (response) => {
        console.log(`✅ Client API Response: ${response.status} ${response.config.url}`);
        return response;
      },
      (error) => {
        console.error(`❌ Client API Response Error: ${error.response?.status} ${error.config?.url}`, error.response?.data);
        
        // Handle authentication errors
        if (error.response?.status === 401) {
          const errorData = error.response.data as ApiError;
          if (errorData.code === 'TOKEN_EXPIRED') {
            // Redirect to refresh page or handle token refresh
            window.location.href = '/auth/refresh?returnUrl=' + encodeURIComponent(window.location.pathname);
            return Promise.reject(error);
          }
          
          if (errorData.code === 'AUTHENTICATION_REQUIRED' || errorData.code === 'AUTHENTICATION_FAILED') {
            // Redirect to login
            window.location.href = '/login?returnUrl=' + encodeURIComponent(window.location.pathname);
            return Promise.reject(error);
          }
        }
        
        return Promise.reject(error);
      }
    );
  }
  
  /**
   * Make a request using the proxy endpoint
   */
  async request<T = any>(config: AxiosRequestConfig): Promise<ApiResponse<T>> {
    try {
      const response: AxiosResponse<T> = await this.axiosInstance.request(config);
      return {
        data: response.data,
        status: response.status,
        statusText: response.statusText
      };
    } catch (error: any) {
      // Re-throw with additional context
      if (error.response) {
        const apiError: ApiError = error.response.data;
        throw new Error(apiError.error || 'API request failed');
      }
      throw error;
    }
  }
  
  /**
   * GET request using proxy
   */
  async get<T = any>(endpoint: string, params?: Record<string, any>): Promise<ApiResponse<T>> {
    return this.request<T>({
      method: 'GET',
      url: endpoint,
      params
    });
  }
  
  /**
   * POST request using proxy
   */
  async post<T = any>(endpoint: string, data?: any): Promise<ApiResponse<T>> {
    return this.request<T>({
      method: 'POST',
      url: endpoint,
      data
    });
  }
  
  /**
   * PUT request using proxy
   */
  async put<T = any>(endpoint: string, data?: any): Promise<ApiResponse<T>> {
    return this.request<T>({
      method: 'PUT',
      url: endpoint,
      data
    });
  }
  
  /**
   * PATCH request using proxy
   */
  async patch<T = any>(endpoint: string, data?: any): Promise<ApiResponse<T>> {
    return this.request<T>({
      method: 'PATCH',
      url: endpoint,
      data
    });
  }
  
  /**
   * DELETE request using proxy
   */
  async delete<T = any>(endpoint: string): Promise<ApiResponse<T>> {
    return this.request<T>({
      method: 'DELETE',
      url: endpoint
    });
  }
  
  /**
   * Make a request to the generic proxy endpoint
   * This forwards the request to the backend API
   */
  async proxy<T = any>(backendEndpoint: string, config: AxiosRequestConfig = {}): Promise<ApiResponse<T>> {
    // Remove leading slash from backend endpoint
    const cleanEndpoint = backendEndpoint.startsWith('/') ? backendEndpoint.slice(1) : backendEndpoint;
    
    return this.request<T>({
      ...config,
      url: `/proxy/${cleanEndpoint}`
    });
  }
}

/**
 * Utility functions for common API patterns
 */

/**
 * Convert backend endpoint to proxy endpoint
 */
export function toProxyEndpoint(backendEndpoint: string): string {
  const cleanEndpoint = backendEndpoint.startsWith('/') ? backendEndpoint.slice(1) : backendEndpoint;
  return `/api/proxy/${cleanEndpoint}`;
}

/**
 * Convert backend endpoint to specific API route if available
 */
export function toApiEndpoint(backendEndpoint: string): string {
  // Map common backend endpoints to specific API routes
  const endpointMappings: Record<string, string> = {
    '/api/admin/users': '/api/admin/users',
    '/api/admin/register': '/api/admin/register',
    '/api/members': '/api/members',
    '/api/organizations': '/api/organizations',
    '/api/roles': '/api/roles',
    '/api/member-verification/total-verified-members': '/api/member-verification/total-verified-members',
    // Add more mappings as needed
  };
  
  // Check if we have a specific mapping
  const specificEndpoint = endpointMappings[backendEndpoint];
  if (specificEndpoint) {
    return specificEndpoint;
  }
  
  // Fall back to proxy endpoint
  return toProxyEndpoint(backendEndpoint);
}

/**
 * Create a configured client instance
 */
export const clientApi = new ClientApiClient();

/**
 * Convenience functions using the default client
 */
export const api = {
  get: <T = any>(endpoint: string, params?: Record<string, any>) => 
    clientApi.get<T>(toApiEndpoint(endpoint), params),
    
  post: <T = any>(endpoint: string, data?: any) => 
    clientApi.post<T>(toApiEndpoint(endpoint), data),
    
  put: <T = any>(endpoint: string, data?: any) => 
    clientApi.put<T>(toApiEndpoint(endpoint), data),
    
  patch: <T = any>(endpoint: string, data?: any) => 
    clientApi.patch<T>(toApiEndpoint(endpoint), data),
    
  delete: <T = any>(endpoint: string) => 
    clientApi.delete<T>(toApiEndpoint(endpoint)),
    
  // Direct proxy access
  proxy: <T = any>(backendEndpoint: string, config?: AxiosRequestConfig) => 
    clientApi.proxy<T>(backendEndpoint, config)
};

/**
 * Admin-specific API functions
 */
export const adminApi = {
  // Admin users
  getUsers: (params?: Record<string, any>) => 
    api.get('/api/admin/users', params),
    
  getUser: (id: string) => 
    api.get(`/api/admin/users/${id}`),
    
  createUser: (userData: any) => 
    api.post('/api/admin/users', userData),
    
  updateUser: (id: string, userData: any) => 
    api.put(`/api/admin/users/${id}`, userData),
    
  deleteUser: (id: string) => 
    api.delete(`/api/admin/users/${id}`),
    
  // Other admin endpoints can be added here
};

/**
 * Member-specific API functions (for Auth0 users)
 */
export const memberApi = {
  // Member endpoints would go here
  // These would use the proxy since they're for Auth0 users
  getProfile: () => 
    api.proxy('/api/member/profile'),
    
  updateProfile: (profileData: any) => 
    api.proxy('/api/member/profile', { method: 'PUT', data: profileData }),
};

// Export types and main client
export type { ClientApiConfig, ApiError, ApiResponse };
