/**
 * Server-side API client for making authenticated requests to the Python backend
 * This runs on the Next.js server and handles authentication token extraction from cookies
 */

import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';
import { cookies } from 'next/headers';
import { NextRequest } from 'next/server';
import jwt from 'jsonwebtoken';

// Types
export interface ServerApiClientConfig {
  baseURL?: string;
  timeout?: number;
  headers?: Record<string, string>;
}

export interface AuthTokens {
  accessToken: string | null;
  idToken: string | null;
  refreshToken: string | null;
  userType: 'admin' | 'member' | null;
}

export interface ApiProxyRequest {
  method: 'GET' | 'POST' | 'PUT' | 'PATCH' | 'DELETE';
  endpoint: string;
  data?: any;
  params?: Record<string, any>;
  headers?: Record<string, string>;
}

export interface ApiProxyResponse<T = any> {
  data: T;
  status: number;
  statusText: string;
  headers: Record<string, string>;
}

/**
 * Extract authentication tokens from cookies (server-side)
 */
export function extractAuthTokensFromCookies(): AuthTokens {
  const cookieStore = cookies();
  
  // Try to get Cognito tokens (admin users)
  const cognitoAccessToken = cookieStore.get('cognito_access_token')?.value;
  const cognitoIdToken = cookieStore.get('cognito_id_token')?.value;
  const cognitoRefreshToken = cookieStore.get('cognito_refresh_token')?.value;
  
  if (cognitoAccessToken && cognitoIdToken) {
    return {
      accessToken: cognitoAccessToken,
      idToken: cognitoIdToken,
      refreshToken: cognitoRefreshToken || null,
      userType: 'admin'
    };
  }
  
  // Try to get legacy token
  const legacyToken = cookieStore.get('token')?.value;
  if (legacyToken) {
    return {
      accessToken: legacyToken,
      idToken: null,
      refreshToken: null,
      userType: 'admin' // Assume admin for legacy tokens
    };
  }
  
  // For Auth0 (member users), we'll need to handle session differently
  // This will be implemented when we add Auth0 server-side token extraction
  
  return {
    accessToken: null,
    idToken: null,
    refreshToken: null,
    userType: null
  };
}

/**
 * Extract authentication tokens from NextRequest (for middleware/API routes)
 */
export function extractAuthTokensFromRequest(request: NextRequest): AuthTokens {
  // Try to get Cognito tokens (admin users)
  const cognitoAccessToken = request.cookies.get('cognito_access_token')?.value;
  const cognitoIdToken = request.cookies.get('cognito_id_token')?.value;
  const cognitoRefreshToken = request.cookies.get('cognito_refresh_token')?.value;
  
  if (cognitoAccessToken && cognitoIdToken) {
    return {
      accessToken: cognitoAccessToken,
      idToken: cognitoIdToken,
      refreshToken: cognitoRefreshToken || null,
      userType: 'admin'
    };
  }
  
  // Try to get legacy token
  const legacyToken = request.cookies.get('token')?.value;
  if (legacyToken) {
    return {
      accessToken: legacyToken,
      idToken: null,
      refreshToken: null,
      userType: 'admin'
    };
  }
  
  return {
    accessToken: null,
    idToken: null,
    refreshToken: null,
    userType: null
  };
}

/**
 * Validate if a JWT token is expired
 */
export function isTokenExpired(token: string): boolean {
  try {
    const decoded = jwt.decode(token, { complete: true });
    if (!decoded || typeof decoded === 'string' || !decoded.payload) {
      return true;
    }
    
    const payload = decoded.payload as any;
    const currentTime = Math.floor(Date.now() / 1000);
    return currentTime >= payload.exp;
  } catch (error) {
    console.error('Token validation error:', error);
    return true;
  }
}

/**
 * Server-side API client class
 */
export class ServerApiClient {
  private axiosInstance: AxiosInstance;
  private baseURL: string;
  
  constructor(config: ServerApiClientConfig = {}) {
    this.baseURL = config.baseURL || process.env.BACKEND_API_URL || process.env.NEXT_PUBLIC_BACKEND_API_URL || 'http://localhost:8000';
    
    this.axiosInstance = axios.create({
      baseURL: this.baseURL,
      timeout: config.timeout || 30000,
      headers: {
        'Content-Type': 'application/json',
        ...config.headers
      }
    });
    
    // Add request interceptor for logging
    this.axiosInstance.interceptors.request.use(
      (config) => {
        console.log(`🚀 Server API Request: ${config.method?.toUpperCase()} ${config.url}`);
        return config;
      },
      (error) => {
        console.error('❌ Server API Request Error:', error);
        return Promise.reject(error);
      }
    );
    
    // Add response interceptor for logging
    this.axiosInstance.interceptors.response.use(
      (response) => {
        console.log(`✅ Server API Response: ${response.status} ${response.config.url}`);
        return response;
      },
      (error) => {
        console.error(`❌ Server API Response Error: ${error.response?.status} ${error.config?.url}`, error.response?.data);
        return Promise.reject(error);
      }
    );
  }
  
  /**
   * Make an authenticated request to the backend API
   */
  async makeAuthenticatedRequest<T = any>(
    request: ApiProxyRequest,
    authTokens: AuthTokens
  ): Promise<ApiProxyResponse<T>> {
    if (!authTokens.accessToken) {
      throw new Error('No access token available for authentication');
    }
    
    // Check if token is expired
    if (isTokenExpired(authTokens.accessToken)) {
      throw new Error('Access token is expired');
    }
    
    const config: AxiosRequestConfig = {
      method: request.method,
      url: request.endpoint,
      data: request.data,
      params: request.params,
      headers: {
        'Authorization': `Bearer ${authTokens.accessToken}`,
        ...request.headers
      }
    };
    
    try {
      const response: AxiosResponse<T> = await this.axiosInstance.request(config);
      
      return {
        data: response.data,
        status: response.status,
        statusText: response.statusText,
        headers: response.headers as Record<string, string>
      };
    } catch (error: any) {
      // Handle specific error cases
      if (error.response?.status === 401) {
        throw new Error('Authentication failed - token may be invalid or expired');
      }
      
      if (error.response?.status === 403) {
        throw new Error('Access forbidden - insufficient permissions');
      }
      
      if (error.response?.status >= 500) {
        throw new Error('Backend server error');
      }
      
      throw error;
    }
  }
  
  /**
   * Convenience methods for different HTTP verbs
   */
  async get<T = any>(endpoint: string, params?: Record<string, any>, authTokens?: AuthTokens): Promise<ApiProxyResponse<T>> {
    const tokens = authTokens || extractAuthTokensFromCookies();
    return this.makeAuthenticatedRequest<T>({ method: 'GET', endpoint, params }, tokens);
  }
  
  async post<T = any>(endpoint: string, data?: any, authTokens?: AuthTokens): Promise<ApiProxyResponse<T>> {
    const tokens = authTokens || extractAuthTokensFromCookies();
    return this.makeAuthenticatedRequest<T>({ method: 'POST', endpoint, data }, tokens);
  }
  
  async put<T = any>(endpoint: string, data?: any, authTokens?: AuthTokens): Promise<ApiProxyResponse<T>> {
    const tokens = authTokens || extractAuthTokensFromCookies();
    return this.makeAuthenticatedRequest<T>({ method: 'PUT', endpoint, data }, tokens);
  }
  
  async patch<T = any>(endpoint: string, data?: any, authTokens?: AuthTokens): Promise<ApiProxyResponse<T>> {
    const tokens = authTokens || extractAuthTokensFromCookies();
    return this.makeAuthenticatedRequest<T>({ method: 'PATCH', endpoint, data }, tokens);
  }
  
  async delete<T = any>(endpoint: string, authTokens?: AuthTokens): Promise<ApiProxyResponse<T>> {
    const tokens = authTokens || extractAuthTokensFromCookies();
    return this.makeAuthenticatedRequest<T>({ method: 'DELETE', endpoint }, tokens);
  }
}

// Export a default instance
export const serverApiClient = new ServerApiClient();

// Export utility functions
export {
  extractAuthTokensFromCookies,
  extractAuthTokensFromRequest,
  isTokenExpired
};
