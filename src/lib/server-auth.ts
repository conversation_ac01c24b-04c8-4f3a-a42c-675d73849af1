/**
 * Server-side authentication utilities for handling both Cognito and Auth0 tokens
 */

import { NextRequest } from 'next/server';
import { cookies } from 'next/headers';
import { auth0 } from './auth0';
import jwt from 'jsonwebtoken';

export interface ServerAuthContext {
  isAuthenticated: boolean;
  userType: 'admin' | 'member' | null;
  accessToken: string | null;
  idToken: string | null;
  refreshToken: string | null;
  user?: any; // User profile data
  needsRefresh?: boolean;
}

/**
 * Get authentication context from cookies (for server components)
 */
export async function getServerAuthContext(): Promise<ServerAuthContext> {
  const cookieStore = cookies();
  
  // Check for Cognito tokens (admin users)
  const cognitoAccessToken = cookieStore.get('cognito_access_token')?.value;
  const cognitoIdToken = cookieStore.get('cognito_id_token')?.value;
  const cognitoRefreshToken = cookieStore.get('cognito_refresh_token')?.value;
  
  if (cognitoAccessToken && cognitoIdToken) {
    const isExpired = isTokenExpired(cognitoAccessToken);
    
    if (!isExpired) {
      return {
        isAuthenticated: true,
        userType: 'admin',
        accessToken: cognitoAccessToken,
        idToken: cognitoIdToken,
        refreshToken: cognitoRefreshToken || null,
        user: extractUserFromToken(cognitoIdToken)
      };
    } else if (cognitoRefreshToken) {
      return {
        isAuthenticated: false,
        userType: 'admin',
        accessToken: cognitoAccessToken,
        idToken: cognitoIdToken,
        refreshToken: cognitoRefreshToken,
        needsRefresh: true
      };
    }
  }
  
  // Check for legacy token
  const legacyToken = cookieStore.get('token')?.value;
  if (legacyToken && !isTokenExpired(legacyToken)) {
    return {
      isAuthenticated: true,
      userType: 'admin',
      accessToken: legacyToken,
      idToken: null,
      refreshToken: null,
      user: extractUserFromToken(legacyToken)
    };
  }
  
  // For Auth0 (member users), we need to handle this differently
  // Auth0 uses session-based authentication, not direct token cookies
  // This would require server-side session handling
  
  return {
    isAuthenticated: false,
    userType: null,
    accessToken: null,
    idToken: null,
    refreshToken: null
  };
}

/**
 * Get authentication context from NextRequest (for API routes and middleware)
 */
export async function getAuthContextFromRequest(request: NextRequest): Promise<ServerAuthContext> {
  // Check for Cognito tokens (admin users)
  const cognitoAccessToken = request.cookies.get('cognito_access_token')?.value;
  const cognitoIdToken = request.cookies.get('cognito_id_token')?.value;
  const cognitoRefreshToken = request.cookies.get('cognito_refresh_token')?.value;
  
  if (cognitoAccessToken && cognitoIdToken) {
    const isExpired = isTokenExpired(cognitoAccessToken);
    
    if (!isExpired) {
      return {
        isAuthenticated: true,
        userType: 'admin',
        accessToken: cognitoAccessToken,
        idToken: cognitoIdToken,
        refreshToken: cognitoRefreshToken || null,
        user: extractUserFromToken(cognitoIdToken)
      };
    } else if (cognitoRefreshToken) {
      return {
        isAuthenticated: false,
        userType: 'admin',
        accessToken: cognitoAccessToken,
        idToken: cognitoIdToken,
        refreshToken: cognitoRefreshToken,
        needsRefresh: true
      };
    }
  }
  
  // Check for Auth0 session (member users)
  try {
    const auth0Session = await auth0.getSession(request);
    if (auth0Session && auth0Session.user) {
      // For Auth0, we might need to get an access token for API calls
      // This depends on your Auth0 configuration and whether you're storing API tokens
      return {
        isAuthenticated: true,
        userType: 'member',
        accessToken: auth0Session.accessToken || null,
        idToken: auth0Session.idToken || null,
        refreshToken: null,
        user: auth0Session.user
      };
    }
  } catch (error) {
    console.error('Error getting Auth0 session:', error);
  }
  
  // Check for legacy token
  const legacyToken = request.cookies.get('token')?.value;
  if (legacyToken && !isTokenExpired(legacyToken)) {
    return {
      isAuthenticated: true,
      userType: 'admin',
      accessToken: legacyToken,
      idToken: null,
      refreshToken: null,
      user: extractUserFromToken(legacyToken)
    };
  }
  
  return {
    isAuthenticated: false,
    userType: null,
    accessToken: null,
    idToken: null,
    refreshToken: null
  };
}

/**
 * Check if a JWT token is expired
 */
export function isTokenExpired(token: string): boolean {
  try {
    if (!token || token.trim() === '') {
      return true;
    }
    
    const decoded = jwt.decode(token, { complete: true });
    if (!decoded || typeof decoded === 'string' || !decoded.payload) {
      return true;
    }
    
    const payload = decoded.payload as any;
    const currentTime = Math.floor(Date.now() / 1000);
    return currentTime >= payload.exp;
  } catch (error) {
    console.error('Token validation error:', error);
    return true;
  }
}

/**
 * Extract user information from JWT token
 */
export function extractUserFromToken(token: string): any {
  try {
    const decoded = jwt.decode(token, { complete: true });
    if (!decoded || typeof decoded === 'string' || !decoded.payload) {
      return null;
    }
    
    const payload = decoded.payload as any;
    
    // Extract common user fields
    return {
      sub: payload.sub,
      email: payload.email,
      username: payload.username || payload['cognito:username'],
      name: payload.name,
      given_name: payload.given_name,
      family_name: payload.family_name,
      roles: payload['custom:roles'] || payload.roles || [],
      groups: payload['cognito:groups'] || [],
      exp: payload.exp,
      iat: payload.iat
    };
  } catch (error) {
    console.error('Error extracting user from token:', error);
    return null;
  }
}

/**
 * Validate authentication for API routes
 */
export async function validateApiAuthentication(request: NextRequest): Promise<{
  isValid: boolean;
  authContext: ServerAuthContext;
  error?: string;
}> {
  const authContext = await getAuthContextFromRequest(request);
  
  if (!authContext.isAuthenticated) {
    if (authContext.needsRefresh) {
      return {
        isValid: false,
        authContext,
        error: 'Token expired - refresh required'
      };
    }
    
    return {
      isValid: false,
      authContext,
      error: 'Authentication required'
    };
  }
  
  if (!authContext.accessToken) {
    return {
      isValid: false,
      authContext,
      error: 'No access token available'
    };
  }
  
  return {
    isValid: true,
    authContext
  };
}

/**
 * Create authorization header for backend API calls
 */
export function createAuthorizationHeader(accessToken: string): Record<string, string> {
  return {
    'Authorization': `Bearer ${accessToken}`
  };
}

/**
 * Check if user has required role/permission
 */
export function hasRole(authContext: ServerAuthContext, requiredRole: string): boolean {
  if (!authContext.user) return false;
  
  const userRoles = authContext.user.roles || [];
  const userGroups = authContext.user.groups || [];
  
  return userRoles.includes(requiredRole) || userGroups.includes(requiredRole);
}

/**
 * Check if user has any of the required roles
 */
export function hasAnyRole(authContext: ServerAuthContext, requiredRoles: string[]): boolean {
  return requiredRoles.some(role => hasRole(authContext, role));
}

/**
 * Get user ID from auth context
 */
export function getUserId(authContext: ServerAuthContext): string | null {
  if (!authContext.user) return null;
  return authContext.user.sub || authContext.user.username || null;
}

/**
 * Get user email from auth context
 */
export function getUserEmail(authContext: ServerAuthContext): string | null {
  if (!authContext.user) return null;
  return authContext.user.email || null;
}

// Export types and main functions
export type { ServerAuthContext };
export {
  getServerAuthContext,
  getAuthContextFromRequest,
  validateApiAuthentication,
  createAuthorizationHeader,
  hasRole,
  hasAnyRole,
  getUserId,
  getUserEmail,
  isTokenExpired,
  extractUserFromToken
};
