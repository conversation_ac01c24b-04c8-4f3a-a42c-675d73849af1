/**
 * Test component for server-side API proxy
 * Tests the /api/member-verification/total-verified-members endpoint
 */

'use client';

import React, { useState } from 'react';
import { memberVerificationAPI, useTotalVerifiedMembers } from '@/services/memberVerificationAPI';

interface TestResult {
  success: boolean;
  data?: any;
  error?: string;
  responseTime?: number;
  timestamp: string;
}

export function ProxyTestComponent() {
  const [testResults, setTestResults] = useState<TestResult[]>([]);
  const [isManualTesting, setIsManualTesting] = useState(false);

  // Using the React hook
  const { data: hookData, loading: hookLoading, error: hookError, refetch } = useTotalVerifiedMembers();

  const runManualTest = async () => {
    setIsManualTesting(true);
    const startTime = performance.now();
    
    try {
      console.log('🧪 Starting manual proxy test...');
      
      const result = await memberVerificationAPI.getTotalVerifiedMembers();
      const endTime = performance.now();
      const responseTime = endTime - startTime;
      
      const testResult: TestResult = {
        success: true,
        data: result,
        responseTime,
        timestamp: new Date().toISOString()
      };
      
      setTestResults(prev => [testResult, ...prev]);
      console.log('✅ Manual test completed successfully:', testResult);
      
    } catch (error: any) {
      const endTime = performance.now();
      const responseTime = endTime - startTime;
      
      const testResult: TestResult = {
        success: false,
        error: error.message,
        responseTime,
        timestamp: new Date().toISOString()
      };
      
      setTestResults(prev => [testResult, ...prev]);
      console.error('❌ Manual test failed:', testResult);
    } finally {
      setIsManualTesting(false);
    }
  };

  const runBrowserConsoleTest = () => {
    console.log('🧪 Running browser console test...');
    console.log('You can also run this manually in the browser console:');
    console.log('memberVerificationAPI.testProxyEndpoint()');
    
    // Make the API available globally for testing
    (window as any).memberVerificationAPI = memberVerificationAPI;
    
    memberVerificationAPI.testProxyEndpoint()
      .then(() => console.log('✅ Browser console test completed'))
      .catch(error => console.error('❌ Browser console test failed:', error));
  };

  const clearResults = () => {
    setTestResults([]);
  };

  return (
    <div style={{ 
      padding: '20px', 
      border: '2px solid #007acc', 
      borderRadius: '8px', 
      margin: '20px',
      backgroundColor: '#f8f9fa',
      fontFamily: 'monospace'
    }}>
      <h3 style={{ color: '#007acc', marginBottom: '20px' }}>
        🧪 Server-Side API Proxy Test
      </h3>
      
      <div style={{ marginBottom: '20px' }}>
        <strong>Endpoint:</strong> <code>/api/member-verification/total-verified-members</code>
      </div>

      {/* React Hook Test */}
      <div style={{ marginBottom: '20px', padding: '15px', backgroundColor: '#e9ecef', borderRadius: '4px' }}>
        <h4>React Hook Test (Auto-loading)</h4>
        {hookLoading && <div style={{ color: '#007acc' }}>🔄 Loading...</div>}
        {hookError && <div style={{ color: '#dc3545' }}>❌ Error: {hookError}</div>}
        {hookData && (
          <div style={{ color: '#28a745' }}>
            ✅ Success: {JSON.stringify(hookData, null, 2)}
          </div>
        )}
        <button 
          onClick={refetch}
          disabled={hookLoading}
          style={{ 
            marginTop: '10px', 
            padding: '5px 10px', 
            backgroundColor: '#007acc', 
            color: 'white', 
            border: 'none', 
            borderRadius: '4px',
            cursor: hookLoading ? 'not-allowed' : 'pointer'
          }}
        >
          🔄 Refetch
        </button>
      </div>

      {/* Manual Test Buttons */}
      <div style={{ marginBottom: '20px' }}>
        <button 
          onClick={runManualTest}
          disabled={isManualTesting}
          style={{ 
            marginRight: '10px', 
            padding: '10px 15px', 
            backgroundColor: '#28a745', 
            color: 'white', 
            border: 'none', 
            borderRadius: '4px',
            cursor: isManualTesting ? 'not-allowed' : 'pointer'
          }}
        >
          {isManualTesting ? '🔄 Testing...' : '🧪 Run Manual Test'}
        </button>
        
        <button 
          onClick={runBrowserConsoleTest}
          style={{ 
            marginRight: '10px', 
            padding: '10px 15px', 
            backgroundColor: '#6f42c1', 
            color: 'white', 
            border: 'none', 
            borderRadius: '4px',
            cursor: 'pointer'
          }}
        >
          🖥️ Console Test
        </button>
        
        <button 
          onClick={clearResults}
          style={{ 
            padding: '10px 15px', 
            backgroundColor: '#6c757d', 
            color: 'white', 
            border: 'none', 
            borderRadius: '4px',
            cursor: 'pointer'
          }}
        >
          🗑️ Clear Results
        </button>
      </div>

      {/* Test Results */}
      {testResults.length > 0 && (
        <div>
          <h4>Test Results ({testResults.length})</h4>
          <div style={{ maxHeight: '400px', overflowY: 'auto' }}>
            {testResults.map((result, index) => (
              <div 
                key={index}
                style={{ 
                  padding: '10px', 
                  margin: '5px 0', 
                  backgroundColor: result.success ? '#d4edda' : '#f8d7da',
                  border: `1px solid ${result.success ? '#c3e6cb' : '#f5c6cb'}`,
                  borderRadius: '4px',
                  fontSize: '12px'
                }}
              >
                <div style={{ fontWeight: 'bold', marginBottom: '5px' }}>
                  {result.success ? '✅ SUCCESS' : '❌ FAILED'} - {result.timestamp}
                  {result.responseTime && ` (${result.responseTime.toFixed(2)}ms)`}
                </div>
                {result.success && result.data && (
                  <pre style={{ margin: 0, whiteSpace: 'pre-wrap' }}>
                    {JSON.stringify(result.data, null, 2)}
                  </pre>
                )}
                {!result.success && result.error && (
                  <div style={{ color: '#721c24' }}>
                    Error: {result.error}
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Instructions */}
      <div style={{ marginTop: '20px', padding: '15px', backgroundColor: '#fff3cd', borderRadius: '4px' }}>
        <h4>Testing Instructions:</h4>
        <ol style={{ margin: 0, paddingLeft: '20px' }}>
          <li>The React Hook test runs automatically when the component loads</li>
          <li>Click "Run Manual Test" to test the API call manually</li>
          <li>Click "Console Test" to run tests in the browser console</li>
          <li>Check the browser console for detailed logs</li>
          <li>Verify that requests go through the Next.js server (not directly to backend)</li>
        </ol>
        
        <div style={{ marginTop: '10px' }}>
          <strong>Expected Behavior:</strong>
          <ul style={{ margin: '5px 0', paddingLeft: '20px' }}>
            <li>✅ No VPN required for the request to work</li>
            <li>✅ Authentication handled server-side</li>
            <li>✅ Request goes to <code>/api/member-verification/total-verified-members</code></li>
            <li>✅ Response contains verified members count</li>
          </ul>
        </div>
      </div>
    </div>
  );
}

export default ProxyTestComponent;
