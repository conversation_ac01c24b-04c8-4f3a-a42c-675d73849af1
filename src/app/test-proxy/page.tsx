/**
 * Test page for server-side API proxy
 * Navigate to /test-proxy to test the endpoint
 */

'use client';

import React from 'react';
import ProxyTestComponent from '@/components/test/ProxyTestComponent';

export default function TestProxyPage() {
  return (
    <div style={{ 
      minHeight: '100vh', 
      backgroundColor: '#f8f9fa', 
      padding: '20px' 
    }}>
      <div style={{ 
        maxWidth: '1200px', 
        margin: '0 auto' 
      }}>
        <h1 style={{ 
          textAlign: 'center', 
          color: '#007acc', 
          marginBottom: '30px',
          fontSize: '2.5rem'
        }}>
          🧪 Server-Side API Proxy Test
        </h1>
        
        <div style={{ 
          backgroundColor: 'white', 
          padding: '20px', 
          borderRadius: '8px', 
          boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
          marginBottom: '20px'
        }}>
          <h2>Testing Endpoint: <code>/api/member-verification/total-verified-members</code></h2>
          <p>
            This page tests the server-side API proxy implementation. The endpoint should:
          </p>
          <ul>
            <li>✅ Work without VPN access</li>
            <li>✅ Handle authentication server-side</li>
            <li>✅ Forward requests to the Python backend</li>
            <li>✅ Return verified members count</li>
          </ul>
        </div>

        <ProxyTestComponent />
        
        <div style={{ 
          backgroundColor: 'white', 
          padding: '20px', 
          borderRadius: '8px', 
          boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
          marginTop: '20px'
        }}>
          <h3>Manual Testing Commands</h3>
          <p>You can also test the endpoint manually using these methods:</p>
          
          <div style={{ marginBottom: '15px' }}>
            <strong>1. Browser Console:</strong>
            <pre style={{ 
              backgroundColor: '#f8f9fa', 
              padding: '10px', 
              borderRadius: '4px',
              overflow: 'auto'
            }}>
{`// Test the API directly
fetch('/api/member-verification/total-verified-members')
  .then(response => response.json())
  .then(data => console.log('Success:', data))
  .catch(error => console.error('Error:', error));

// Or use the service
memberVerificationAPI.testProxyEndpoint();`}
            </pre>
          </div>

          <div style={{ marginBottom: '15px' }}>
            <strong>2. cURL (from terminal):</strong>
            <pre style={{ 
              backgroundColor: '#f8f9fa', 
              padding: '10px', 
              borderRadius: '4px',
              overflow: 'auto'
            }}>
{`curl -X GET http://localhost:3000/api/member-verification/total-verified-members \\
  -H "Content-Type: application/json" \\
  -H "Cookie: cognito_access_token=YOUR_TOKEN"`}
            </pre>
          </div>

          <div>
            <strong>3. Network Tab:</strong>
            <p>
              Open browser DevTools → Network tab → Run the test above → 
              Verify the request goes to <code>/api/member-verification/total-verified-members</code> 
              (not directly to the backend)
            </p>
          </div>
        </div>

        <div style={{ 
          backgroundColor: '#e9ecef', 
          padding: '15px', 
          borderRadius: '8px', 
          marginTop: '20px',
          textAlign: 'center'
        }}>
          <p style={{ margin: 0, color: '#6c757d' }}>
            <strong>Note:</strong> Make sure you're logged in as an admin or member user to test authentication.
            If you get authentication errors, try logging in first.
          </p>
        </div>
      </div>
    </div>
  );
}
