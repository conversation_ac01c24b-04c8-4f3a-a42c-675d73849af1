import { AuthProvider } from "@/components/AuthProvider";
import { CognitoTokenSync } from "@/components/CognitoTokenSync";
import { QueryProvider } from "@/components/QueryProvider";
import { ReduxProvider } from "@/components/ReduxProvider";
import { ThemeProvider } from "@/components/ThemeProvider";
import WarningSupressor from "@/components/WarningSupressor";
import "@/utils/suppressWarnings";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mon<PERSON> } from "next/font/google";
import { Toaster } from "react-hot-toast";
import "./globals.css";
import { SessionManager } from "@/components/auth/SessionManager";
import GlobalRouterSetter from "@/components/GlobalRouterSetter";
import { Amplify } from "aws-amplify";
import Auth0ProviderWrapper from "@/components/auth/Auth0Provider";

// Get Cognito config from environment variables
const region = process.env.NEXT_PUBLIC_AWS_REGION! as string;
const userPoolId = process.env.NEXT_PUBLIC_AWS_USER_POOL_ID! as string;
const userPoolClientId = process.env.NEXT_PUBLIC_AWS_CLIENT_ID! as string;

if (!region || !userPoolId || !userPoolClientId) {
  throw new Error("Missing required AWS Cognito environment variables");
}

// Only include supported fields per Amplify docs/types
const amplifyConfig = {
  Auth: {
    Cognito: {
      region,
      userPoolId,
      userPoolClientId,
      // Optional: allow email login
      loginWith: {
        email: true,
      },
      // Optional: sign-up verification method (must be 'code' or 'link')
      signUpVerificationMethod: "code" as const,
    },
  },
};

// Configure Amplify with Cognito
Amplify.configure(amplifyConfig);

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <head>
        <script
          dangerouslySetInnerHTML={{
            __html: `
              if (typeof window !== 'undefined' && process.env.NODE_ENV === 'development') {
                const originalWarn = console.warn;
                console.warn = (...args) => {
                  const message = args[0];
                  if (typeof message === 'string' && 
                      (message.includes('Skipping auto-scroll behavior') || 
                       message.includes('position: sticky') || 
                       message.includes('position: fixed'))) {
                    return;
                  }
                  originalWarn.apply(console, args);
                };
              }
            `,
          }}
        />
      </head>
      <body
        className={`${geistSans.variable} ${geistMono.variable}`}
        suppressHydrationWarning
      >
        <WarningSupressor  />
        <GlobalRouterSetter />
        <CognitoTokenSync
          autoSync={true}
          debug={process.env.NODE_ENV === "development"}
        />
        <SessionManager
          checkInterval={30000}
          refreshBuffer={5}
          maxRetries={3}
          retryDelay={1000}
          maxRetryDelay={10000}
          enableNetworkAwareness={true}
          inactivityTimeout={30}
          enableInactivityLogout={true}
          warningTime={0.5}
          showWarningDialog={true}
          warningAutoCloseTime={60}
          siteRevisitTimeoutHours={1}
          enableSiteRevisitTimeout={true}
          checkSiteRevisitOnMount={true}
          checkSiteRevisitOnFocus={true}
          checkSiteRevisitOnVisibilityChange={true}
        />
        <ReduxProvider>
          <QueryProvider>
            <ThemeProvider>
              <Auth0ProviderWrapper>
                <AuthProvider>
                  {children}
                  <Toaster
                    position="top-right"
                    toastOptions={{
                      duration: 4000,
                      style: {
                        background: "#363636",
                        color: "#fff",
                      },
                    }}
                  />
                </AuthProvider>
              </Auth0ProviderWrapper>
            </ThemeProvider>
          </QueryProvider>
        </ReduxProvider>
      </body>
    </html>
  );
}
