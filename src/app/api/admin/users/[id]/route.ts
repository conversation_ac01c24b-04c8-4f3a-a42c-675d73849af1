/**
 * Individual Admin User API endpoint - Server-side proxy to Python backend
 * Handles operations on specific admin users (GET, PUT, DELETE)
 */

import { NextRequest, NextResponse } from 'next/server';
import { serverApiClient } from '@/lib/server-api-client';
import { validateApiAuthentication, hasAnyRole } from '@/lib/server-auth';

interface UpdateAdminUserRequest {
  email?: string;
  username?: string;
  roles?: string[];
  firstName?: string;
  lastName?: string;
  phone?: string;
  countrycode?: string;
  isActive?: boolean;
}

/**
 * GET /api/admin/users/[id] - Get specific admin user
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Validate authentication
    const authValidation = await validateApiAuthentication(request);
    if (!authValidation.isValid) {
      return NextResponse.json(
        { error: authValidation.error || 'Authentication required' },
        { status: 401 }
      );
    }

    const { authContext } = authValidation;

    // Check if user has admin permissions
    if (authContext.userType !== 'admin') {
      return NextResponse.json(
        { error: 'Admin access required' },
        { status: 403 }
      );
    }

    const userId = params.id;
    if (!userId) {
      return NextResponse.json(
        { error: 'User ID is required' },
        { status: 400 }
      );
    }

    console.log(`🔍 Fetching admin user: ${userId}`);

    // Make request to backend
    const response = await serverApiClient.get(
      `/api/admin/users/${userId}`,
      undefined,
      {
        accessToken: authContext.accessToken,
        idToken: authContext.idToken,
        refreshToken: authContext.refreshToken,
        userType: authContext.userType
      }
    );

    return NextResponse.json(response.data, { status: response.status });

  } catch (error: any) {
    console.error(`❌ Error fetching admin user ${params.id}:`, error);

    if (error.response?.status === 404) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }

    if (error.message?.includes('Authentication failed')) {
      return NextResponse.json(
        { error: 'Authentication failed' },
        { status: 401 }
      );
    }

    return NextResponse.json(
      { 
        error: 'Failed to fetch admin user',
        details: process.env.NODE_ENV === 'development' ? error.message : undefined
      },
      { status: error.response?.status || 500 }
    );
  }
}

/**
 * PUT /api/admin/users/[id] - Update specific admin user
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Validate authentication
    const authValidation = await validateApiAuthentication(request);
    if (!authValidation.isValid) {
      return NextResponse.json(
        { error: authValidation.error || 'Authentication required' },
        { status: 401 }
      );
    }

    const { authContext } = authValidation;

    // Check if user has admin permissions
    if (authContext.userType !== 'admin') {
      return NextResponse.json(
        { error: 'Admin access required' },
        { status: 403 }
      );
    }

    // Check for user update permissions
    const requiredRoles = ['admin', 'super_admin', 'user_manager'];
    if (!hasAnyRole(authContext, requiredRoles)) {
      return NextResponse.json(
        { error: 'Insufficient permissions to update users' },
        { status: 403 }
      );
    }

    const userId = params.id;
    if (!userId) {
      return NextResponse.json(
        { error: 'User ID is required' },
        { status: 400 }
      );
    }

    // Parse request body
    const updateData: UpdateAdminUserRequest = await request.json();

    console.log(`✏️ Updating admin user: ${userId}`);

    // Make request to backend
    const response = await serverApiClient.put(
      `/api/admin/users/${userId}`,
      updateData,
      {
        accessToken: authContext.accessToken,
        idToken: authContext.idToken,
        refreshToken: authContext.refreshToken,
        userType: authContext.userType
      }
    );

    return NextResponse.json(response.data, { status: response.status });

  } catch (error: any) {
    console.error(`❌ Error updating admin user ${params.id}:`, error);

    if (error.response?.status === 404) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }

    if (error.response?.status === 400) {
      return NextResponse.json(
        error.response.data || { error: 'Invalid update data' },
        { status: 400 }
      );
    }

    if (error.message?.includes('Authentication failed')) {
      return NextResponse.json(
        { error: 'Authentication failed' },
        { status: 401 }
      );
    }

    return NextResponse.json(
      { 
        error: 'Failed to update admin user',
        details: process.env.NODE_ENV === 'development' ? error.message : undefined
      },
      { status: error.response?.status || 500 }
    );
  }
}

/**
 * DELETE /api/admin/users/[id] - Delete specific admin user
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Validate authentication
    const authValidation = await validateApiAuthentication(request);
    if (!authValidation.isValid) {
      return NextResponse.json(
        { error: authValidation.error || 'Authentication required' },
        { status: 401 }
      );
    }

    const { authContext } = authValidation;

    // Check if user has admin permissions
    if (authContext.userType !== 'admin') {
      return NextResponse.json(
        { error: 'Admin access required' },
        { status: 403 }
      );
    }

    // Check for user deletion permissions (more restrictive)
    const requiredRoles = ['super_admin'];
    if (!hasAnyRole(authContext, requiredRoles)) {
      return NextResponse.json(
        { error: 'Super admin permissions required to delete users' },
        { status: 403 }
      );
    }

    const userId = params.id;
    if (!userId) {
      return NextResponse.json(
        { error: 'User ID is required' },
        { status: 400 }
      );
    }

    // Prevent self-deletion
    const currentUserId = authContext.user?.sub || authContext.user?.username;
    if (userId === currentUserId) {
      return NextResponse.json(
        { error: 'Cannot delete your own account' },
        { status: 400 }
      );
    }

    console.log(`🗑️ Deleting admin user: ${userId}`);

    // Make request to backend
    const response = await serverApiClient.delete(
      `/api/admin/users/${userId}`,
      {
        accessToken: authContext.accessToken,
        idToken: authContext.idToken,
        refreshToken: authContext.refreshToken,
        userType: authContext.userType
      }
    );

    return NextResponse.json(response.data, { status: response.status });

  } catch (error: any) {
    console.error(`❌ Error deleting admin user ${params.id}:`, error);

    if (error.response?.status === 404) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }

    if (error.message?.includes('Authentication failed')) {
      return NextResponse.json(
        { error: 'Authentication failed' },
        { status: 401 }
      );
    }

    return NextResponse.json(
      { 
        error: 'Failed to delete admin user',
        details: process.env.NODE_ENV === 'development' ? error.message : undefined
      },
      { status: error.response?.status || 500 }
    );
  }
}

/**
 * Handle CORS preflight requests
 */
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, PUT, DELETE, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      'Access-Control-Max-Age': '86400',
    },
  });
}
