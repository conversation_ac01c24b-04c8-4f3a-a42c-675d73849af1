/**
 * Admin Users API endpoint - Server-side proxy to Python backend
 * Handles admin user management operations with proper authentication
 */

import { NextRequest, NextResponse } from 'next/server';
import { serverApiClient } from '@/lib/server-api-client';
import { validateApiAuthentication, hasAnyRole } from '@/lib/server-auth';

// Types for admin user operations
interface CreateAdminUserRequest {
  email: string;
  username: string;
  password: string;
  roles?: string[];
  firstName?: string;
  lastName?: string;
  phone?: string;
  countrycode?: string;
}

interface UpdateAdminUserRequest {
  email?: string;
  username?: string;
  roles?: string[];
  firstName?: string;
  lastName?: string;
  phone?: string;
  countrycode?: string;
  isActive?: boolean;
}

interface AdminUserFilters {
  page?: number;
  limit?: number;
  search?: string;
  role?: string;
  isActive?: boolean;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

/**
 * GET /api/admin/users - List admin users with filtering and pagination
 */
export async function GET(request: NextRequest) {
  try {
    // Validate authentication
    const authValidation = await validateApiAuthentication(request);
    if (!authValidation.isValid) {
      return NextResponse.json(
        { error: authValidation.error || 'Authentication required' },
        { status: 401 }
      );
    }

    const { authContext } = authValidation;

    // Check if user has admin permissions
    if (authContext.userType !== 'admin') {
      return NextResponse.json(
        { error: 'Admin access required' },
        { status: 403 }
      );
    }

    // Check for specific admin roles if needed
    const requiredRoles = ['admin', 'super_admin', 'user_manager'];
    if (!hasAnyRole(authContext, requiredRoles)) {
      return NextResponse.json(
        { error: 'Insufficient permissions for user management' },
        { status: 403 }
      );
    }

    // Extract query parameters
    const url = new URL(request.url);
    const filters: AdminUserFilters = {
      page: url.searchParams.get('page') ? parseInt(url.searchParams.get('page')!) : 1,
      limit: url.searchParams.get('limit') ? parseInt(url.searchParams.get('limit')!) : 20,
      search: url.searchParams.get('search') || undefined,
      role: url.searchParams.get('role') || undefined,
      isActive: url.searchParams.get('isActive') ? url.searchParams.get('isActive') === 'true' : undefined,
      sortBy: url.searchParams.get('sortBy') || 'created_at',
      sortOrder: (url.searchParams.get('sortOrder') as 'asc' | 'desc') || 'desc'
    };

    console.log(`🔍 Fetching admin users with filters:`, filters);

    // Make request to backend
    const response = await serverApiClient.get(
      '/api/admin/users',
      filters,
      {
        accessToken: authContext.accessToken,
        idToken: authContext.idToken,
        refreshToken: authContext.refreshToken,
        userType: authContext.userType
      }
    );

    return NextResponse.json(response.data, { status: response.status });

  } catch (error: any) {
    console.error('❌ Error fetching admin users:', error);

    if (error.message?.includes('Authentication failed')) {
      return NextResponse.json(
        { error: 'Authentication failed' },
        { status: 401 }
      );
    }

    if (error.response?.status === 403) {
      return NextResponse.json(
        { error: 'Access forbidden' },
        { status: 403 }
      );
    }

    return NextResponse.json(
      { 
        error: 'Failed to fetch admin users',
        details: process.env.NODE_ENV === 'development' ? error.message : undefined
      },
      { status: error.response?.status || 500 }
    );
  }
}

/**
 * POST /api/admin/users - Create new admin user
 */
export async function POST(request: NextRequest) {
  try {
    // Validate authentication
    const authValidation = await validateApiAuthentication(request);
    if (!authValidation.isValid) {
      return NextResponse.json(
        { error: authValidation.error || 'Authentication required' },
        { status: 401 }
      );
    }

    const { authContext } = authValidation;

    // Check if user has admin permissions
    if (authContext.userType !== 'admin') {
      return NextResponse.json(
        { error: 'Admin access required' },
        { status: 403 }
      );
    }

    // Check for user creation permissions
    const requiredRoles = ['admin', 'super_admin', 'user_manager'];
    if (!hasAnyRole(authContext, requiredRoles)) {
      return NextResponse.json(
        { error: 'Insufficient permissions to create users' },
        { status: 403 }
      );
    }

    // Parse request body
    const userData: CreateAdminUserRequest = await request.json();

    // Basic validation
    if (!userData.email || !userData.username || !userData.password) {
      return NextResponse.json(
        { error: 'Email, username, and password are required' },
        { status: 400 }
      );
    }

    console.log(`👤 Creating admin user: ${userData.email}`);

    // Make request to backend
    const response = await serverApiClient.post(
      '/api/admin/users',
      userData,
      {
        accessToken: authContext.accessToken,
        idToken: authContext.idToken,
        refreshToken: authContext.refreshToken,
        userType: authContext.userType
      }
    );

    return NextResponse.json(response.data, { status: response.status });

  } catch (error: any) {
    console.error('❌ Error creating admin user:', error);

    if (error.message?.includes('Authentication failed')) {
      return NextResponse.json(
        { error: 'Authentication failed' },
        { status: 401 }
      );
    }

    if (error.response?.status === 400) {
      return NextResponse.json(
        error.response.data || { error: 'Invalid user data' },
        { status: 400 }
      );
    }

    if (error.response?.status === 409) {
      return NextResponse.json(
        { error: 'User already exists' },
        { status: 409 }
      );
    }

    return NextResponse.json(
      { 
        error: 'Failed to create admin user',
        details: process.env.NODE_ENV === 'development' ? error.message : undefined
      },
      { status: error.response?.status || 500 }
    );
  }
}

/**
 * Handle CORS preflight requests
 */
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      'Access-Control-Max-Age': '86400',
    },
  });
}
