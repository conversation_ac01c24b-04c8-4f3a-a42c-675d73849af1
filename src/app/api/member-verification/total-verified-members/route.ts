/**
 * Member Verification API endpoint - Server-side proxy to Python backend
 * GET /api/member-verification/total-verified-members
 */

import { NextRequest, NextResponse } from 'next/server';
import { serverApiClient } from '@/lib/server-api-client';
import { validateApiAuthentication, hasAnyRole } from '@/lib/server-auth';
import { validateRequest, getSecurityHeaders, sanitizeError } from '@/lib/api-security';

// Types for the response
interface TotalVerifiedMembersResponse {
  total_verified_members: number;
  last_updated?: string;
  success: boolean;
  message?: string;
}

/**
 * GET /api/member-verification/total-verified-members
 * Returns the total count of verified members
 */
export async function GET(request: NextRequest) {
  try {
    console.log('🔍 Processing request for total verified members');

    // Validate request security
    const securityValidation = validateRequest(request);
    if (!securityValidation.isValid) {
      console.warn('🚨 Security validation failed:', securityValidation.errors);
      return NextResponse.json(
        { 
          error: 'Request validation failed',
          details: securityValidation.errors
        },
        { status: 400, headers: getSecurityHeaders() }
      );
    }

    // Validate authentication
    const authValidation = await validateApiAuthentication(request);
    if (!authValidation.isValid) {
      console.warn('🔐 Authentication failed:', authValidation.error);
      return NextResponse.json(
        { 
          error: authValidation.error || 'Authentication required',
          code: 'AUTHENTICATION_REQUIRED'
        },
        { status: 401, headers: getSecurityHeaders() }
      );
    }

    const { authContext } = authValidation;

    // Check if user has appropriate permissions
    // This endpoint might be accessible to both admin and member users
    if (!authContext.isAuthenticated) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401, headers: getSecurityHeaders() }
      );
    }

    // Optional: Check for specific roles if needed
    // const requiredRoles = ['admin', 'super_admin', 'member'];
    // if (!hasAnyRole(authContext, requiredRoles)) {
    //   return NextResponse.json(
    //     { error: 'Insufficient permissions' },
    //     { status: 403, headers: getSecurityHeaders() }
    //   );
    // }

    console.log(`🔍 Fetching total verified members for user: ${authContext.user?.email || 'unknown'}`);
    console.log(`👤 User type: ${authContext.userType}`);

    // Make request to backend
    const response = await serverApiClient.get<TotalVerifiedMembersResponse>(
      '/api/member-verification/total-verified-members',
      undefined, // no query parameters
      {
        accessToken: authContext.accessToken,
        idToken: authContext.idToken,
        refreshToken: authContext.refreshToken,
        userType: authContext.userType
      }
    );

    console.log(`✅ Successfully fetched total verified members: ${response.data.total_verified_members || 'unknown'}`);

    // Create response with security headers
    const responseHeaders = new Headers();
    const securityHeaders = getSecurityHeaders();
    Object.entries(securityHeaders).forEach(([key, value]) => {
      responseHeaders.set(key, value);
    });

    // Add CORS headers
    responseHeaders.set('Access-Control-Allow-Origin', '*');
    responseHeaders.set('Access-Control-Allow-Methods', 'GET, OPTIONS');
    responseHeaders.set('Access-Control-Allow-Headers', 'Content-Type, Authorization');

    return NextResponse.json(response.data, {
      status: response.status,
      headers: responseHeaders
    });

  } catch (error: any) {
    console.error('❌ Error fetching total verified members:', error);

    const isDevelopment = process.env.NODE_ENV === 'development';
    const securityHeaders = getSecurityHeaders();

    // Handle specific error types
    if (error.message?.includes('Authentication failed') || error.message?.includes('Token expired')) {
      return NextResponse.json(
        { 
          error: 'Authentication failed',
          code: 'AUTHENTICATION_FAILED',
          details: isDevelopment ? error.message : undefined
        },
        { status: 401, headers: securityHeaders }
      );
    }

    if (error.response?.status === 403) {
      return NextResponse.json(
        { 
          error: 'Access forbidden',
          code: 'ACCESS_FORBIDDEN',
          details: isDevelopment ? error.message : undefined
        },
        { status: 403, headers: securityHeaders }
      );
    }

    if (error.response?.status === 404) {
      return NextResponse.json(
        { 
          error: 'Endpoint not found',
          code: 'NOT_FOUND',
          details: isDevelopment ? error.message : undefined
        },
        { status: 404, headers: securityHeaders }
      );
    }

    if (error.code === 'ECONNREFUSED' || error.code === 'ENOTFOUND') {
      return NextResponse.json(
        { 
          error: 'Backend service unavailable',
          code: 'SERVICE_UNAVAILABLE',
          details: isDevelopment ? 'Unable to connect to backend service' : undefined
        },
        { status: 503, headers: securityHeaders }
      );
    }

    if (error.response) {
      // Backend returned an error response
      return NextResponse.json(
        error.response.data || { 
          error: 'Backend error',
          code: 'BACKEND_ERROR',
          details: isDevelopment ? error.message : undefined
        },
        { status: error.response.status, headers: securityHeaders }
      );
    }

    // Generic error
    const sanitizedError = sanitizeError(error, isDevelopment);
    return NextResponse.json(
      { 
        error: sanitizedError,
        code: 'INTERNAL_ERROR',
        details: isDevelopment ? error.message : undefined
      },
      { status: 500, headers: securityHeaders }
    );
  }
}

/**
 * Handle CORS preflight requests
 */
export async function OPTIONS() {
  const securityHeaders = getSecurityHeaders();
  
  return new NextResponse(null, {
    status: 200,
    headers: {
      ...securityHeaders,
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      'Access-Control-Max-Age': '86400',
    },
  });
}
