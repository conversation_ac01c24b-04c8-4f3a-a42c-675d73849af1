/**
 * Member Verification API endpoint - Simple test endpoint
 * GET /api/member-verification/total-verified-members
 */

import { NextRequest, NextResponse } from 'next/server';

// Types for the response
interface TotalVerifiedMembersResponse {
  total_verified_members: number;
  last_updated?: string;
  success: boolean;
  message?: string;
}

/**
 * Simple authentication check using cookies
 */
function isAuthenticated(request: NextRequest): boolean {
  // Check for Cognito tokens (admin users)
  const cognitoAccessToken = request.cookies.get('cognito_access_token')?.value;
  const cognitoIdToken = request.cookies.get('cognito_id_token')?.value;

  // Check for legacy token
  const legacyToken = request.cookies.get('token')?.value;

  return !!(cognitoAccessToken && cognitoIdToken) || !!legacyToken;
}

/**
 * GET /api/member-verification/total-verified-members
 * Returns the total count of verified members
 */
export async function GET(request: NextRequest) {
  try {
    console.log('🔍 Processing request for total verified members');

    // Simple authentication check
    if (!isAuthenticated(request)) {
      console.warn('🔐 Authentication failed: No valid tokens found');
      return NextResponse.json(
        {
          error: 'Authentication required',
          code: 'AUTHENTICATION_REQUIRED'
        },
        { status: 401 }
      );
    }

    console.log('✅ User authenticated, proceeding with request');

    // For testing purposes, return mock data
    // In a real implementation, this would call your Python backend
    const mockResponse: TotalVerifiedMembersResponse = {
      total_verified_members: 1234,
      last_updated: new Date().toISOString(),
      success: true,
      message: 'Successfully retrieved verified members count (mock data)'
    };

    console.log(`✅ Returning mock data: ${mockResponse.total_verified_members} verified members`);

    return NextResponse.json(mockResponse, {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      }
    });

  } catch (error: any) {
    console.error('❌ Error processing request:', error);

    const isDevelopment = process.env.NODE_ENV === 'development';

    return NextResponse.json(
      {
        error: 'Internal server error',
        code: 'INTERNAL_ERROR',
        details: isDevelopment ? error.message : 'An unexpected error occurred'
      },
      { status: 500 }
    );
  }
}

/**
 * Handle CORS preflight requests
 */
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      'Access-Control-Max-Age': '86400',
    },
  });
}
