/**
 * Dynamic API proxy route that forwards requests to the Python backend
 * This handles authentication and acts as a server-side proxy
 * 
 * Usage: /api/proxy/[backend-endpoint]
 * Example: /api/proxy/api/admin/users -> forwards to {BACKEND_URL}/api/admin/users
 */

import { NextRequest, NextResponse } from 'next/server';
import { serverApiClient } from '@/lib/server-api-client';
import { validateApiAuthentication } from '@/lib/server-auth';
import { validateRequest, getSecurityHeaders, sanitizeError } from '@/lib/api-security';

// Supported HTTP methods
const SUPPORTED_METHODS = ['GET', 'POST', 'PUT', 'PATCH', 'DELETE'] as const;
type SupportedMethod = typeof SUPPORTED_METHODS[number];

/**
 * Generic handler for all HTTP methods
 */
async function handleRequest(
  request: NextRequest,
  { params }: { params: { path: string[] } }
) {
  try {
    const method = request.method as SupportedMethod;

    // Check if method is supported
    if (!SUPPORTED_METHODS.includes(method)) {
      return NextResponse.json(
        { error: `Method ${method} not allowed` },
        { status: 405, headers: getSecurityHeaders() }
      );
    }

    // Validate request security
    const securityValidation = validateRequest(request);
    if (!securityValidation.isValid) {
      console.warn('🚨 Security validation failed:', securityValidation.errors);
      return NextResponse.json(
        {
          error: 'Request validation failed',
          details: securityValidation.errors
        },
        { status: 400, headers: getSecurityHeaders() }
      );
    }
    
    // Validate authentication
    const authValidation = await validateApiAuthentication(request);
    if (!authValidation.isValid) {
      return NextResponse.json(
        { 
          error: authValidation.error || 'Authentication failed',
          code: 'AUTHENTICATION_REQUIRED'
        },
        { status: 401 }
      );
    }
    
    const { authContext } = authValidation;
    
    // Build the backend endpoint path
    const backendPath = '/' + params.path.join('/');
    
    // Extract query parameters
    const url = new URL(request.url);
    const queryParams: Record<string, any> = {};
    url.searchParams.forEach((value, key) => {
      queryParams[key] = value;
    });
    
    // Extract request body for non-GET requests
    let requestData: any = undefined;
    if (['POST', 'PUT', 'PATCH'].includes(method)) {
      try {
        const contentType = request.headers.get('content-type') || '';
        
        if (contentType.includes('application/json')) {
          requestData = await request.json();
        } else if (contentType.includes('application/x-www-form-urlencoded')) {
          const formData = await request.formData();
          requestData = Object.fromEntries(formData.entries());
        } else if (contentType.includes('multipart/form-data')) {
          // Handle file uploads if needed
          requestData = await request.formData();
        } else {
          requestData = await request.text();
        }
      } catch (error) {
        console.error('Error parsing request body:', error);
        // Continue with undefined body if parsing fails
      }
    }
    
    // Extract additional headers to forward
    const forwardHeaders: Record<string, string> = {};
    const headersToForward = ['content-type', 'accept', 'user-agent'];
    
    headersToForward.forEach(headerName => {
      const headerValue = request.headers.get(headerName);
      if (headerValue) {
        forwardHeaders[headerName] = headerValue;
      }
    });
    
    // Make the authenticated request to the backend
    console.log(`🔄 Proxying ${method} request to: ${backendPath}`);
    console.log(`🔑 User type: ${authContext.userType}, User: ${authContext.user?.email || 'unknown'}`);
    
    const response = await serverApiClient.makeAuthenticatedRequest(
      {
        method,
        endpoint: backendPath,
        data: requestData,
        params: Object.keys(queryParams).length > 0 ? queryParams : undefined,
        headers: forwardHeaders
      },
      {
        accessToken: authContext.accessToken,
        idToken: authContext.idToken,
        refreshToken: authContext.refreshToken,
        userType: authContext.userType
      }
    );
    
    // Create response with proper headers
    const responseHeaders = new Headers();

    // Add security headers
    const securityHeaders = getSecurityHeaders();
    Object.entries(securityHeaders).forEach(([key, value]) => {
      responseHeaders.set(key, value);
    });

    // Forward important headers from backend
    const headersToForwardBack = ['content-type', 'cache-control', 'etag'];
    headersToForwardBack.forEach(headerName => {
      const headerValue = response.headers[headerName];
      if (headerValue) {
        responseHeaders.set(headerName, headerValue);
      }
    });

    // Add CORS headers if needed (override security headers for CORS)
    responseHeaders.set('Access-Control-Allow-Origin', '*');
    responseHeaders.set('Access-Control-Allow-Methods', 'GET, POST, PUT, PATCH, DELETE, OPTIONS');
    responseHeaders.set('Access-Control-Allow-Headers', 'Content-Type, Authorization');
    
    console.log(`✅ Proxy response: ${response.status} ${backendPath}`);
    
    return NextResponse.json(response.data, {
      status: response.status,
      headers: responseHeaders
    });
    
  } catch (error: any) {
    console.error('❌ Proxy error:', error);

    const isDevelopment = process.env.NODE_ENV === 'development';
    const securityHeaders = getSecurityHeaders();

    // Handle specific error types
    if (error.message?.includes('Authentication failed')) {
      return NextResponse.json(
        {
          error: 'Authentication failed',
          code: 'AUTHENTICATION_FAILED',
          details: isDevelopment ? error.message : undefined
        },
        { status: 401, headers: securityHeaders }
      );
    }
    
    if (error.message?.includes('Access forbidden')) {
      return NextResponse.json(
        { 
          error: 'Access forbidden',
          code: 'ACCESS_FORBIDDEN',
          details: error.message
        },
        { status: 403 }
      );
    }
    
    if (error.message?.includes('expired')) {
      return NextResponse.json(
        { 
          error: 'Token expired',
          code: 'TOKEN_EXPIRED',
          details: error.message
        },
        { status: 401 }
      );
    }
    
    if (error.response) {
      // Backend returned an error response
      return NextResponse.json(
        error.response.data || { 
          error: 'Backend error',
          code: 'BACKEND_ERROR',
          details: error.message
        },
        { status: error.response.status }
      );
    }
    
    if (error.code === 'ECONNREFUSED' || error.code === 'ENOTFOUND') {
      return NextResponse.json(
        { 
          error: 'Backend service unavailable',
          code: 'SERVICE_UNAVAILABLE',
          details: 'Unable to connect to backend service'
        },
        { status: 503 }
      );
    }
    
    // Generic error
    return NextResponse.json(
      { 
        error: 'Internal server error',
        code: 'INTERNAL_ERROR',
        details: process.env.NODE_ENV === 'development' ? error.message : 'An unexpected error occurred'
      },
      { status: 500 }
    );
  }
}

// Handle OPTIONS requests for CORS
export async function OPTIONS(request: NextRequest) {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, PUT, PATCH, DELETE, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      'Access-Control-Max-Age': '86400',
    },
  });
}

// Export handlers for all supported methods
export async function GET(request: NextRequest, context: { params: { path: string[] } }) {
  return handleRequest(request, context);
}

export async function POST(request: NextRequest, context: { params: { path: string[] } }) {
  return handleRequest(request, context);
}

export async function PUT(request: NextRequest, context: { params: { path: string[] } }) {
  return handleRequest(request, context);
}

export async function PATCH(request: NextRequest, context: { params: { path: string[] } }) {
  return handleRequest(request, context);
}

export async function DELETE(request: NextRequest, context: { params: { path: string[] } }) {
  return handleRequest(request, context);
}
