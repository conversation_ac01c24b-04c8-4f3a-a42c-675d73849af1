import { NextResponse } from "next/server";
import type { NextRequest } from "next/server";
import { adminMiddleware } from "./middleware/admin-middleware";
import { memberMiddleware } from "./middleware/member-middleware";
import { auth0 } from "./lib/auth0";

// Public routes that don't require authentication
const PUBLIC_ROUTES = ["/", "/login", "/forgot-password"];

// Auth0 routes that should be handled by Auth0 middleware
const AUTH0_ROUTES = [
  "/auth/login",
  "/auth/logout",
  "/auth/callback",
  "/callback",
];

export async function middleware(request: NextRequest) {
  // Skip middleware for static and internal files
  if (
    request.nextUrl.pathname.startsWith("/api/") ||
    request.nextUrl.pathname.startsWith("/_next/") ||
    request.nextUrl.pathname.match(/\.(ico|png|jpg|js|css|svg)$/)
  ) {
    return NextResponse.next();
  }

  console.log("Main Middleware - Processing path:", request.nextUrl.pathname);

  const pathname = request.nextUrl.pathname;

  // Check if the current route is a public route
  const isPublicRoute = PUBLIC_ROUTES.includes(pathname);

  if (isPublicRoute) {
    console.log(
      "Main Middleware - Public route, checking authentication status"
    );

    // Check Auth0 session for member authentication
    const auth0Session = await auth0.getSession(request);

    // Check Cognito tokens for admin authentication
    const accessToken = request.cookies.get("cognito_access_token")?.value;
    const idToken = request.cookies.get("cognito_id_token")?.value;
    const isAdminAuthenticated = accessToken && idToken;

    console.log(
      "Main Middleware - Auth0 session:",
      !!auth0Session,
      "Admin tokens:",
      !!isAdminAuthenticated
    );

    // If member is authenticated, redirect to member dashboard
    if (auth0Session) {
      console.log(
        "Main Middleware - Authenticated member accessing public route, redirecting to member dashboard"
      );
      return NextResponse.redirect(
        new URL("/member-user/dashboard", request.url)
      );
    }

    // If admin is authenticated, redirect to admin dashboard
    if (isAdminAuthenticated) {
      console.log(
        "Main Middleware - Authenticated admin accessing public route, redirecting to admin dashboard"
      );
      return NextResponse.redirect(new URL("/dashboard", request.url));
    }

    // If not authenticated, allow access to public routes
    console.log(
      "Main Middleware - Unauthenticated user, allowing access to public route:",
      pathname
    );
    return NextResponse.next();
  }

  // Handle Auth0 routes - let Auth0 middleware handle these
  const isAuth0Route =
    AUTH0_ROUTES.includes(pathname) || pathname.startsWith("/auth/");

  if (isAuth0Route) {
    console.log(
      "Main Middleware - Auth0 route, delegating to Auth0 middleware:",
      pathname
    );
    const auth0Result = await auth0.middleware(request);
    if (auth0Result) {
      console.log("Main Middleware - Handled by Auth0 middleware");
      return auth0Result;
    }
    // If Auth0 middleware doesn't handle it, continue
    return NextResponse.next();
  }

  // Try member middleware for member routes
  const memberResult = await memberMiddleware(request);
  if (memberResult) {
    console.log("Main Middleware - Handled by member middleware");
    return memberResult;
  }

  // Try admin middleware for admin routes
  const adminResult = adminMiddleware(request);
  if (adminResult) {
    console.log("Main Middleware - Handled by admin middleware");
    return adminResult;
  }

  // For all other routes, require authentication via Auth0
  const session = await auth0.getSession(request);
  if (!session) {
    console.log(
      "Main Middleware - Unauthenticated access to protected route, redirecting to login page"
    );
    // Redirect to the login page instead of /auth/login to let Auth0 handle the flow properly
    const loginUrl = new URL("/login", request.url);
    loginUrl.searchParams.set("returnTo", pathname);
    return NextResponse.redirect(loginUrl);
  }

  // If neither middleware handled it, allow the request to continue
  console.log("Main Middleware - No specific middleware handling, continuing");
  return NextResponse.next();
}

export const config = {
  matcher: ["/((?!api|_next/static|_next/image|favicon\\.ico).*)"],
};
